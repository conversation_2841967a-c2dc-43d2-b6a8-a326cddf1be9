{"name": "@mui/lab", "//": "version should be 'alpha' at all time", "version": "7.0.0-beta.14", "author": "MUI Team", "description": "Laboratory for new Material UI modules.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "material-ui", "material design", "lab"], "repository": {"type": "git", "url": "git+https://github.com/mui/material-ui.git", "directory": "packages/mui-lab"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/material-ui/about-the-lab/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.27.1", "clsx": "^2.1.1", "prop-types": "^15.8.1", "@mui/system": "^7.1.1", "@mui/utils": "^7.1.1", "@mui/types": "^7.4.3"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0", "@mui/material": "^7.1.2", "@mui/material-pigment-css": "^7.1.1"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}, "@mui/material-pigment-css": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=14.0.0"}, "private": false, "module": "./esm/index.js", "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./index.d.ts", "default": "./index.js"}, "import": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./*/index.d.ts", "default": "./*/index.js"}, "import": {"types": "./esm/*/index.d.ts", "default": "./esm/*/index.js"}}, "./esm": null, "./modern": null}, "types": "./index.d.ts"}