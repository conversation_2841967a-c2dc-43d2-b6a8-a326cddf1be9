import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Button,
  useTheme,
  useMediaQuery,
  Fade,
  Slide,
  Zoom,
  Grow,
} from '@mui/material'
import {
  ArrowForward,
  Phone,
  Email,
} from '@mui/icons-material'

const HeroSection = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const [loaded, setLoaded] = useState(false)
  const [titleVisible, setTitleVisible] = useState(false)
  const [descriptionVisible, setDescriptionVisible] = useState(false)
  const [buttonsVisible, setButtonsVisible] = useState(false)
  const [contactVisible, setContactVisible] = useState(false)

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  useEffect(() => {
    const timer1 = setTimeout(() => setLoaded(true), 300)
    const timer2 = setTimeout(() => setTitleVisible(true), 800)
    const timer3 = setTimeout(() => setDescriptionVisible(true), 1300)
    const timer4 = setTimeout(() => setButtonsVisible(true), 1800)
    const timer5 = setTimeout(() => setContactVisible(true), 2300)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
      clearTimeout(timer3)
      clearTimeout(timer4)
      clearTimeout(timer5)
    }
  }, [])

  return (
    <Box
      id="home"
      sx={{
        minHeight: '70vh',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        backgroundImage: 'url(https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          zIndex: 1,
        }
      }}
    >
      {/* Professional Main Content */}
      <Container
        maxWidth="lg"
        sx={{
          position: 'relative',
          zIndex: 2,
          py: 4,
          textAlign: 'center',
        }}
      >
        <Box sx={{ color: 'white', maxWidth: '800px', mx: 'auto' }}>
          <Fade in={loaded} timeout={1000}>
            <Typography
              variant="h6"
              sx={{
                fontSize: '1.1rem',
                fontWeight: 500,
                mb: 1.5,
                color: '#ff6b35',
                letterSpacing: '0.1em',
                textTransform: 'uppercase',
                animation: loaded ? 'glow 2s ease-in-out infinite alternate' : 'none',
                '@keyframes glow': {
                  '0%': { textShadow: '0 0 5px #ff6b35' },
                  '100%': { textShadow: '0 0 20px #ff6b35, 0 0 30px #ff6b35' },
                },
              }}
            >
              Welcome to Excellence
            </Typography>
          </Fade>

          <Slide direction="up" in={titleVisible} timeout={1200}>
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                fontWeight: 700,
                mb: 2,
                lineHeight: 1.1,
                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
                background: 'linear-gradient(45deg, #fff 30%, #ff6b35 70%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                animation: titleVisible ? 'shimmer 3s ease-in-out infinite' : 'none',
                '@keyframes shimmer': {
                  '0%': { backgroundPosition: '-200% center' },
                  '100%': { backgroundPosition: '200% center' },
                },
                backgroundSize: '200% auto',
              }}
            >
              Nyanza Technical
              <br />
              Secondary School
            </Typography>
          </Slide>

          <Grow in={descriptionVisible} timeout={1000}>
            <Typography
              variant="h5"
              sx={{
                fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.4rem' },
                fontWeight: 400,
                mb: 3,
                opacity: 0.95,
                lineHeight: 1.4,
                maxWidth: '700px',
                mx: 'auto',
                animation: descriptionVisible ? 'fadeInUp 1s ease-out' : 'none',
                '@keyframes fadeInUp': {
                  '0%': { opacity: 0, transform: 'translateY(30px)' },
                  '100%': { opacity: 0.95, transform: 'translateY(0)' },
                },
              }}
            >
              Empowering students with excellence in TVET education at Nyanza District.
              Level 3-5 certifications with proven national excellence and
              comprehensive technical skill development for Rwanda's future.
            </Typography>
          </Grow>

          <Zoom in={buttonsVisible} timeout={800}>
            <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap', justifyContent: 'center', mb: 3 }}>
              <Button
                variant="contained"
                size="large"
                endIcon={<ArrowForward />}
                onClick={() => scrollToSection('#about')}
                sx={{
                  bgcolor: '#ff6b35',
                  color: 'white',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: 2,
                  textTransform: 'none',
                  boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)',
                  animation: buttonsVisible ? 'pulse 2s ease-in-out infinite' : 'none',
                  '&:hover': {
                    bgcolor: '#e55a2b',
                    transform: 'translateY(-2px) scale(1.05)',
                    boxShadow: '0 6px 20px rgba(255, 107, 53, 0.4)',
                    animation: 'none',
                  },
                  transition: 'all 0.3s ease',
                  '@keyframes pulse': {
                    '0%': { boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)' },
                    '50%': { boxShadow: '0 4px 25px rgba(255, 107, 53, 0.6)' },
                    '100%': { boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)' },
                  },
                }}
              >
                Explore Programs
              </Button>

              <Button
                variant="outlined"
                size="large"
                sx={{
                  color: 'white',
                  borderColor: 'white',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: 2,
                  textTransform: 'none',
                  borderWidth: 2,
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.1)',
                    borderColor: '#ff6b35',
                    color: '#ff6b35',
                    transform: 'translateY(-2px) scale(1.05)',
                    boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Contact Admissions
              </Button>
            </Box>
          </Zoom>

          {/* Contact Info */}
          <Slide direction="up" in={contactVisible} timeout={600}>
            <Box sx={{
              display: 'flex',
              gap: 4,
              flexWrap: 'wrap',
              justifyContent: 'center',
              opacity: 0.9,
              animation: contactVisible ? 'bounceIn 1s ease-out' : 'none',
              '@keyframes bounceIn': {
                '0%': { opacity: 0, transform: 'scale(0.3)' },
                '50%': { opacity: 1, transform: 'scale(1.05)' },
                '70%': { transform: 'scale(0.9)' },
                '100%': { opacity: 0.9, transform: 'scale(1)' },
              },
            }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                '&:hover': {
                  transform: 'scale(1.1)',
                  color: '#ff6b35',
                },
                transition: 'all 0.3s ease',
              }}>
                <Phone sx={{ fontSize: 20 }} />
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  +250 788 123 456
                </Typography>
              </Box>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                '&:hover': {
                  transform: 'scale(1.1)',
                  color: '#ff6b35',
                },
                transition: 'all 0.3s ease',
              }}>
                <Email sx={{ fontSize: 20 }} />
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  <EMAIL>
                </Typography>
              </Box>
            </Box>
          </Slide>
        </Box>
      </Container>
    </Box>
  )
}

export default HeroSection
