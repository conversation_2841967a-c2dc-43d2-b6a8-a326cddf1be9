import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON>pography,
  <PERSON>ton,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Box,
  useTheme,
  useMediaQuery,
  Menu,
  MenuItem,
  Collapse,
  Container,
  Fade,
  Slide,
  Paper,
  Divider,
  Chip,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  ExpandMore,
  ExpandLess,
  Phone,
  Email,
  LocationOn,
  KeyboardArrowDown,
  School,
} from '@mui/icons-material'

const Header = () => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const [openSubmenu, setOpenSubmenu] = useState('')
  const [scrolled, setScrolled] = useState(false)
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'))

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50
      setScrolled(isScrolled)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navigate = useNavigate()

  const menuItems = [
    {
      label: 'Our School',
      submenu: [
        { label: 'About Nyanza TSS', href: '/about', isRoute: true },
        { label: "Principal's Message", href: '/principal', isRoute: true },
        { label: 'Facilities', href: '/facilities', isRoute: true },
        { label: 'Work With Us', href: '/careers', isRoute: true },
      ]
    },
    {
      label: 'Admissions',
      submenu: [
        { label: 'Admissions', href: '/admissions', isRoute: true },
        { label: 'School Fees Structure', href: '/fees', isRoute: true },
      ]
    },
    {
      label: 'Academic Life',
      submenu: [
        { label: 'Academic Staff', href: '/academic-staff', isRoute: true },
        { label: 'Computer Science', href: '/computer-science', isRoute: true },
        { label: 'Technical Skills', href: '/technical-skills', isRoute: true },
      ]
    },
    {
      label: 'Student Life',
      submenu: [
        { label: 'Discover Nyanza TSS', href: '/discover', isRoute: true },
        { label: 'School Calendar', href: '/school-calendar', isRoute: true },
        { label: 'Clubs & Societies', href: '/clubs-societies', isRoute: true },
        { label: 'Sports at Nyanza TSS', href: '/sports', isRoute: true },
        { label: 'Students Handbook', href: '/students-handbook', isRoute: true },
      ]
    },
    { label: 'Contacts', href: '/contacts', isRoute: true },
  ]

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleMenuClick = (event, menuLabel) => {
    if (isMobile) {
      setOpenSubmenu(openSubmenu === menuLabel ? '' : menuLabel)
    } else {
      setAnchorEl(event.currentTarget)
      setOpenSubmenu(menuLabel)
    }
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setOpenSubmenu('')
  }

  const handleNavigation = (href, isRoute = false) => {
    if (isRoute) {
      navigate(href)
    } else {
      const element = document.querySelector(href)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    setMobileOpen(false)
    handleMenuClose()
  }

  const drawer = (
    <Box sx={{ width: 300, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Drawer Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 3,
          borderBottom: '1px solid rgba(255,255,255,0.2)',
          background: 'rgba(255,255,255,0.1)',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <img
            src="/ntss-logo.png"
            alt="NTSS Logo"
            style={{
              height: '45px',
              filter: 'brightness(0) invert(1)',
            }}
          />
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'white' }}>
              Nyanza TSS
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)' }}>
              TVET Excellence
            </Typography>
          </Box>
        </Box>
        <IconButton
          onClick={handleDrawerToggle}
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255,255,255,0.1)',
              transform: 'rotate(90deg)',
            },
            transition: 'all 0.3s ease',
          }}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      {/* Navigation Menu */}
      <Box sx={{ flex: 1, py: 2 }}>
        <List sx={{ px: 1 }}>
          {menuItems.map((item) => (
            <Box key={item.label}>
              <ListItem
                button
                onClick={() => item.submenu ? handleMenuClick(null, item.label) : handleNavigation(item.href, item.isRoute)}
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  mx: 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    transform: 'translateX(8px)',
                  }
                }}
              >
                <Box
                  sx={{
                    width: 4,
                    height: 4,
                    borderRadius: '50%',
                    backgroundColor: '#ff9800',
                    mr: 2,
                  }}
                />
                <ListItemText
                  primary={item.label}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontWeight: 600,
                      fontSize: '1rem',
                    }
                  }}
                />
                {item.submenu && (
                  <Box sx={{ color: 'rgba(255,255,255,0.7)' }}>
                    {openSubmenu === item.label ? <ExpandLess /> : <ExpandMore />}
                  </Box>
                )}
              </ListItem>
              {item.submenu && (
                <Collapse in={openSubmenu === item.label} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.submenu.map((subItem) => (
                      <ListItem
                        key={subItem.label}
                        button
                        sx={{
                          pl: 6,
                          borderRadius: 2,
                          mx: 2,
                          mb: 0.5,
                          '&:hover': {
                            backgroundColor: 'rgba(255,255,255,0.1)',
                          }
                        }}
                        onClick={() => handleNavigation(subItem.href, subItem.isRoute)}
                      >
                        <ListItemText
                          primary={subItem.label}
                          sx={{
                            '& .MuiListItemText-primary': {
                              fontSize: '0.9rem',
                              color: 'rgba(255,255,255,0.9)',
                            }
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              )}
            </Box>
          ))}
        </List>
      </Box>

      {/* Drawer Footer */}
      <Box
        sx={{
          p: 3,
          borderTop: '1px solid rgba(255,255,255,0.2)',
          background: 'rgba(255,255,255,0.1)',
        }}
      >
        <Button
          variant="contained"
          fullWidth
          sx={{
            backgroundColor: '#ff9800',
            color: 'white',
            fontWeight: 600,
            py: 1.5,
            borderRadius: 2,
            '&:hover': {
              backgroundColor: '#f57c00',
            }
          }}
        >
          Apply Now
        </Button>
      </Box>
    </Box>
  )

  return (
    <>
      {/* Top Contact Bar */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 50%, #0d47a1 100%)',
          color: 'white',
          py: 1,
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1301,
          transform: scrolled ? 'translateY(-100%)' : 'translateY(0)',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          boxShadow: '0 2px 10px rgba(25, 118, 210, 0.3)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%)',
            animation: 'shimmer 3s ease-in-out infinite',
            '@keyframes shimmer': {
              '0%': { transform: 'translateX(-100%)' },
              '100%': { transform: 'translateX(100%)' },
            },
          },
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 2,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'wrap' }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    filter: 'brightness(1.2)',
                  }
                }}
              >
                <Phone sx={{
                  fontSize: 16,
                  animation: 'pulse 2s ease-in-out infinite',
                  '@keyframes pulse': {
                    '0%, 100%': { transform: 'scale(1)' },
                    '50%': { transform: 'scale(1.1)' },
                  },
                }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                  +250 788 309 436
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    filter: 'brightness(1.2)',
                  }
                }}
              >
                <Email sx={{
                  fontSize: 16,
                  animation: 'pulse 2s ease-in-out infinite 0.5s',
                  '@keyframes pulse': {
                    '0%, 100%': { transform: 'scale(1)' },
                    '50%': { transform: 'scale(1.1)' },
                  },
                }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                  <EMAIL>
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    filter: 'brightness(1.2)',
                  }
                }}
              >
                <LocationOn sx={{
                  fontSize: 16,
                  animation: 'pulse 2s ease-in-out infinite 1s',
                  '@keyframes pulse': {
                    '0%, 100%': { transform: 'scale(1)' },
                    '50%': { transform: 'scale(1.1)' },
                  },
                }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                  Kigoma Sector, Nyanza District
                </Typography>
              </Box>
            </Box>
            <Chip
              label="Excellence in TVET Education"
              size="small"
              sx={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.15) 100%)',
                color: 'white',
                fontWeight: 600,
                display: { xs: 'none', md: 'flex' },
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.3)',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                transition: 'all 0.3s ease',
                animation: 'glow 2s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { boxShadow: '0 2px 8px rgba(0,0,0,0.1)' },
                  '100%': { boxShadow: '0 4px 16px rgba(255,255,255,0.3)' },
                },
                '&:hover': {
                  transform: 'translateY(-2px) scale(1.05)',
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.35) 0%, rgba(255,255,255,0.25) 100%)',
                },
              }}
            />
          </Box>
        </Container>
      </Box>

      {/* Main Header */}
      <AppBar
        position="fixed"
        sx={{
          background: scrolled
            ? 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%)'
            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
          color: '#333',
          boxShadow: scrolled
            ? '0 8px 32px rgba(0,0,0,0.12), 0 2px 8px rgba(0,0,0,0.08)'
            : '0 4px 20px rgba(0,0,0,0.08), 0 1px 4px rgba(0,0,0,0.04)',
          zIndex: 1300,
          top: scrolled ? 0 : '48px',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          backdropFilter: scrolled ? 'blur(20px) saturate(180%)' : 'blur(10px)',
          borderBottom: scrolled ? '1px solid rgba(0,0,0,0.08)' : '1px solid rgba(0,0,0,0.04)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '1px',
            background: 'linear-gradient(90deg, transparent 0%, rgba(25,118,210,0.5) 50%, transparent 100%)',
            animation: scrolled ? 'none' : 'headerShimmer 4s ease-in-out infinite',
            '@keyframes headerShimmer': {
              '0%, 100%': { opacity: 0 },
              '50%': { opacity: 1 },
            },
          },
        }}
      >
        <Container maxWidth="lg">
          <Toolbar sx={{ justifyContent: 'space-between', py: scrolled ? 0.5 : 1, transition: 'padding 0.3s ease' }}>
            {/* Logo */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                position: 'relative',
                '&:hover': {
                  transform: 'translateY(-2px) scale(1.02)',
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  width: '0%',
                  height: '0%',
                  background: 'radial-gradient(circle, rgba(25,118,210,0.1) 0%, transparent 70%)',
                  borderRadius: '50%',
                  transform: 'translate(-50%, -50%)',
                  transition: 'all 0.3s ease',
                  zIndex: -1,
                },
                '&:hover::before': {
                  width: '120%',
                  height: '120%',
                },
              }}
              onClick={() => navigate('/')}
            >
              <Box
                sx={{
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: '50%',
                    background: 'conic-gradient(from 0deg, transparent 0deg, rgba(25,118,210,0.3) 90deg, transparent 180deg)',
                    animation: 'logoRotate 8s linear infinite',
                    '@keyframes logoRotate': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' },
                    },
                  },
                }}
              >
                <img
                  src="/ntss-logo.png"
                  alt="NTSS Logo"
                  style={{
                    height: scrolled ? '45px' : '55px',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    filter: 'drop-shadow(0 4px 12px rgba(25,118,210,0.2))',
                    position: 'relative',
                    zIndex: 2,
                  }}
                />
              </Box>
              <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 800,
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 50%, #0d47a1 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontSize: scrolled ? '1.1rem' : '1.3rem',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    lineHeight: 1.2,
                    textShadow: '0 2px 4px rgba(25,118,210,0.1)',
                    animation: 'textGlow 3s ease-in-out infinite alternate',
                    '@keyframes textGlow': {
                      '0%': { filter: 'brightness(1)' },
                      '100%': { filter: 'brightness(1.1)' },
                    },
                  }}
                >
                  Nyanza TSS
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: '#64748b',
                    fontSize: '0.75rem',
                    fontWeight: 600,
                    display: 'block',
                    lineHeight: 1,
                    letterSpacing: '0.5px',
                    textTransform: 'uppercase',
                    background: 'linear-gradient(135deg, #64748b 0%, #475569 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}
                >
                  TVET Excellence
                </Typography>
              </Box>
            </Box>

            {/* Desktop Navigation */}
            {!isMobile && (
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                {menuItems.map((item) => (
                  <Box key={item.label}>
                    <Button
                      onClick={(e) => item.submenu ? handleMenuClick(e, item.label) : handleNavigation(item.href, item.isRoute)}
                      endIcon={item.submenu ? <KeyboardArrowDown sx={{
                        transition: 'all 0.3s ease',
                        transform: openSubmenu === item.label ? 'rotate(180deg)' : 'rotate(0deg)',
                      }} /> : null}
                      sx={{
                        color: '#334155',
                        fontWeight: 600,
                        fontSize: '0.95rem',
                        textTransform: 'none',
                        px: 3,
                        py: 1.5,
                        borderRadius: 3,
                        position: 'relative',
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        overflow: 'hidden',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                          color: 'white',
                          transform: 'translateY(-3px) scale(1.02)',
                          boxShadow: '0 8px 25px rgba(25, 118, 210, 0.4), 0 4px 12px rgba(25, 118, 210, 0.3)',
                        },
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: '-100%',
                          width: '100%',
                          height: '100%',
                          background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%)',
                          transition: 'left 0.6s ease',
                        },
                        '&:hover::before': {
                          left: '100%',
                        },
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: '50%',
                          width: 0,
                          height: '3px',
                          background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
                          borderRadius: '2px 2px 0 0',
                          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                          transform: 'translateX(-50%)',
                        },
                        '&:hover::after': {
                          width: '90%',
                        },
                      }}
                    >
                      {item.label}
                    </Button>
                  {item.submenu && (
                    <Menu
                      anchorEl={anchorEl}
                      open={Boolean(anchorEl) && openSubmenu === item.label}
                      onClose={handleMenuClose}
                      sx={{
                        '& .MuiPaper-root': {
                          mt: 1,
                          minWidth: 200,
                          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                        }
                      }}
                    >
                      {item.submenu.map((subItem) => (
                        <MenuItem
                          key={subItem.label}
                          onClick={() => handleNavigation(subItem.href, subItem.isRoute)}
                          sx={{
                            fontSize: '0.9rem',
                            '&:hover': {
                              backgroundColor: '#1976d2',
                              color: 'white',
                            }
                          }}
                        >
                          {subItem.label}
                        </MenuItem>
                      ))}
                    </Menu>
                  )}
                </Box>
              ))}
                <Button
                  variant="contained"
                  sx={{
                    ml: 3,
                    background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 50%, #ef6c00 100%)',
                    color: 'white',
                    textTransform: 'none',
                    fontWeight: 700,
                    fontSize: '0.95rem',
                    px: 4,
                    py: 1.5,
                    borderRadius: 4,
                    position: 'relative',
                    overflow: 'hidden',
                    boxShadow: '0 4px 15px rgba(255, 152, 0, 0.4), 0 2px 8px rgba(255, 152, 0, 0.2)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    animation: 'buttonPulse 3s ease-in-out infinite',
                    '@keyframes buttonPulse': {
                      '0%, 100%': {
                        boxShadow: '0 4px 15px rgba(255, 152, 0, 0.4), 0 2px 8px rgba(255, 152, 0, 0.2)',
                        transform: 'scale(1)',
                      },
                      '50%': {
                        boxShadow: '0 6px 20px rgba(255, 152, 0, 0.6), 0 4px 12px rgba(255, 152, 0, 0.3)',
                        transform: 'scale(1.02)',
                      },
                    },
                    '&:hover': {
                      background: 'linear-gradient(135deg, #f57c00 0%, #ef6c00 50%, #e65100 100%)',
                      transform: 'translateY(-3px) scale(1.05)',
                      boxShadow: '0 8px 25px rgba(255, 152, 0, 0.5), 0 4px 15px rgba(255, 152, 0, 0.3)',
                      animation: 'none',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: '-100%',
                      width: '100%',
                      height: '100%',
                      background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%)',
                      transition: 'left 0.6s ease',
                    },
                    '&:hover::before': {
                      left: '100%',
                    },
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      top: '50%',
                      right: '15px',
                      width: '6px',
                      height: '6px',
                      background: 'rgba(255,255,255,0.8)',
                      borderRadius: '50%',
                      transform: 'translateY(-50%)',
                      animation: 'dot 2s ease-in-out infinite',
                      '@keyframes dot': {
                        '0%, 100%': { opacity: 0.5, transform: 'translateY(-50%) scale(1)' },
                        '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.2)' },
                      },
                    },
                  }}
                  onClick={() => navigate('/admissions')}
                >
                  Apply Now
                </Button>
              </Box>
            )}

            {/* Mobile Menu Button */}
            {isMobile && (
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{
                  background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.1) 100%)',
                  borderRadius: 3,
                  border: '2px solid rgba(25, 118, 210, 0.2)',
                  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                    color: 'white',
                    transform: 'translateY(-2px) rotate(90deg) scale(1.1)',
                    boxShadow: '0 8px 25px rgba(25, 118, 210, 0.4)',
                    border: '2px solid rgba(25, 118, 210, 0.5)',
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: '-100%',
                    width: '100%',
                    height: '100%',
                    background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%)',
                    transition: 'left 0.6s ease',
                  },
                  '&:hover::before': {
                    left: '100%',
                  },
                }}
              >
                <MenuIcon sx={{
                  fontSize: '1.5rem',
                  transition: 'all 0.3s ease',
                }} />
              </IconButton>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: 320,
            background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 50%, #0d47a1 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255,152,0,0.1) 0%, transparent 50%)
              `,
              pointerEvents: 'none',
            },
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '2px',
              background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.5) 50%, transparent 100%)',
              animation: 'drawerShimmer 3s ease-in-out infinite',
              '@keyframes drawerShimmer': {
                '0%, 100%': { opacity: 0 },
                '50%': { opacity: 1 },
              },
            },
          },
          '& .MuiBackdrop-root': {
            backgroundColor: 'rgba(0,0,0,0.6)',
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Spacer for fixed header */}
      <Box sx={{ height: scrolled ? '70px' : '118px', transition: 'height 0.3s ease' }} />
    </>
  )
}

export default Header
