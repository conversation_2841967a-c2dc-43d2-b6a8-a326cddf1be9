import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON>pography,
  <PERSON>ton,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Box,
  useTheme,
  useMediaQuery,
  Menu,
  MenuItem,
  Collapse,
  Container,
  Fade,
  Slide,
  Paper,
  Divider,
  Chip,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  ExpandMore,
  ExpandLess,
  Phone,
  Email,
  LocationOn,
  KeyboardArrowDown,
  School,
} from '@mui/icons-material'

const Header = () => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const [openSubmenu, setOpenSubmenu] = useState('')
  const [scrolled, setScrolled] = useState(false)
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'))

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50
      setScrolled(isScrolled)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navigate = useNavigate()

  const menuItems = [
    {
      label: 'Our School',
      submenu: [
        { label: 'About Nyanza TSS', href: '/about', isRoute: true },
        { label: "Principal's Message", href: '/principal', isRoute: true },
        { label: 'Facilities', href: '/facilities', isRoute: true },
        { label: 'Work With Us', href: '/careers', isRoute: true },
      ]
    },
    {
      label: 'Admissions',
      submenu: [
        { label: 'Admissions', href: '/admissions', isRoute: true },
        { label: 'School Fees Structure', href: '/fees', isRoute: true },
      ]
    },
    {
      label: 'Academic Life',
      submenu: [
        { label: 'Academic Staff', href: '/academic-staff', isRoute: true },
        { label: 'Computer Science', href: '/computer-science', isRoute: true },
        { label: 'Technical Skills', href: '/technical-skills', isRoute: true },
      ]
    },
    {
      label: 'Student Life',
      submenu: [
        { label: 'Discover Nyanza TSS', href: '/discover', isRoute: true },
        { label: 'School Calendar', href: '/school-calendar', isRoute: true },
        { label: 'Clubs & Societies', href: '/clubs-societies', isRoute: true },
        { label: 'Sports at Nyanza TSS', href: '/sports', isRoute: true },
        { label: 'Students Handbook', href: '/students-handbook', isRoute: true },
      ]
    },
    { label: 'Contacts', href: '/contacts', isRoute: true },
  ]

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleMenuClick = (event, menuLabel) => {
    if (isMobile) {
      setOpenSubmenu(openSubmenu === menuLabel ? '' : menuLabel)
    } else {
      setAnchorEl(event.currentTarget)
      setOpenSubmenu(menuLabel)
    }
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setOpenSubmenu('')
  }

  const handleNavigation = (href, isRoute = false) => {
    if (isRoute) {
      navigate(href)
    } else {
      const element = document.querySelector(href)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    setMobileOpen(false)
    handleMenuClose()
  }

  const drawer = (
    <Box sx={{ width: 300, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Drawer Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 3,
          borderBottom: '1px solid rgba(255,255,255,0.2)',
          background: 'rgba(255,255,255,0.1)',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <img
            src="/ntss-logo.png"
            alt="NTSS Logo"
            style={{
              height: '45px',
              filter: 'brightness(0) invert(1)',
            }}
          />
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'white' }}>
              Nyanza TSS
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)' }}>
              TVET Excellence
            </Typography>
          </Box>
        </Box>
        <IconButton
          onClick={handleDrawerToggle}
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255,255,255,0.1)',
              transform: 'rotate(90deg)',
            },
            transition: 'all 0.3s ease',
          }}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      {/* Navigation Menu */}
      <Box sx={{ flex: 1, py: 2 }}>
        <List sx={{ px: 1 }}>
          {menuItems.map((item) => (
            <Box key={item.label}>
              <ListItem
                button
                onClick={() => item.submenu ? handleMenuClick(null, item.label) : handleNavigation(item.href, item.isRoute)}
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  mx: 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    transform: 'translateX(8px)',
                  }
                }}
              >
                <Box
                  sx={{
                    width: 4,
                    height: 4,
                    borderRadius: '50%',
                    backgroundColor: '#ff9800',
                    mr: 2,
                  }}
                />
                <ListItemText
                  primary={item.label}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontWeight: 600,
                      fontSize: '1rem',
                    }
                  }}
                />
                {item.submenu && (
                  <Box sx={{ color: 'rgba(255,255,255,0.7)' }}>
                    {openSubmenu === item.label ? <ExpandLess /> : <ExpandMore />}
                  </Box>
                )}
              </ListItem>
              {item.submenu && (
                <Collapse in={openSubmenu === item.label} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.submenu.map((subItem) => (
                      <ListItem
                        key={subItem.label}
                        button
                        sx={{
                          pl: 6,
                          borderRadius: 2,
                          mx: 2,
                          mb: 0.5,
                          '&:hover': {
                            backgroundColor: 'rgba(255,255,255,0.1)',
                          }
                        }}
                        onClick={() => handleNavigation(subItem.href, subItem.isRoute)}
                      >
                        <ListItemText
                          primary={subItem.label}
                          sx={{
                            '& .MuiListItemText-primary': {
                              fontSize: '0.9rem',
                              color: 'rgba(255,255,255,0.9)',
                            }
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              )}
            </Box>
          ))}
        </List>
      </Box>

      {/* Drawer Footer */}
      <Box
        sx={{
          p: 3,
          borderTop: '1px solid rgba(255,255,255,0.2)',
          background: 'rgba(255,255,255,0.1)',
        }}
      >
        <Button
          variant="contained"
          fullWidth
          sx={{
            backgroundColor: '#ff9800',
            color: 'white',
            fontWeight: 600,
            py: 1.5,
            borderRadius: 2,
            '&:hover': {
              backgroundColor: '#f57c00',
            }
          }}
        >
          Apply Now
        </Button>
      </Box>
    </Box>
  )

  return (
    <>
      {/* Top Contact Bar */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 30%, #0d47a1 70%, #1a237e 100%)',
          color: 'white',
          py: 1.2,
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1301,
          transform: scrolled ? 'translateY(-100%)' : 'translateY(0)',
          transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
          boxShadow: scrolled
            ? '0 4px 20px rgba(25, 118, 210, 0.4)'
            : '0 2px 15px rgba(25, 118, 210, 0.3), 0 8px 32px rgba(25, 118, 210, 0.15)',
          backdropFilter: 'blur(10px) saturate(120%)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%)',
            animation: 'shimmer 3s ease-in-out infinite',
            '@keyframes shimmer': {
              '0%': { transform: 'translateX(-100%)' },
              '100%': { transform: 'translateX(100%)' },
            },
          },
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 2,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'wrap' }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    filter: 'brightness(1.2)',
                  }
                }}
              >
                <Phone sx={{
                  fontSize: 16,
                  animation: 'pulse 2s ease-in-out infinite',
                  '@keyframes pulse': {
                    '0%, 100%': { transform: 'scale(1)' },
                    '50%': { transform: 'scale(1.1)' },
                  },
                }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                  +250 788 309 436
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    filter: 'brightness(1.2)',
                  }
                }}
              >
                <Email sx={{
                  fontSize: 16,
                  animation: 'pulse 2s ease-in-out infinite 0.5s',
                  '@keyframes pulse': {
                    '0%, 100%': { transform: 'scale(1)' },
                    '50%': { transform: 'scale(1.1)' },
                  },
                }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                  <EMAIL>
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    filter: 'brightness(1.2)',
                  }
                }}
              >
                <LocationOn sx={{
                  fontSize: 16,
                  animation: 'pulse 2s ease-in-out infinite 1s',
                  '@keyframes pulse': {
                    '0%, 100%': { transform: 'scale(1)' },
                    '50%': { transform: 'scale(1.1)' },
                  },
                }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                  Kigoma Sector, Nyanza District
                </Typography>
              </Box>
            </Box>
            <Chip
              label="Excellence in TVET Education"
              size="small"
              sx={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.15) 100%)',
                color: 'white',
                fontWeight: 600,
                display: { xs: 'none', md: 'flex' },
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.3)',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                transition: 'all 0.3s ease',
                animation: 'glow 2s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { boxShadow: '0 2px 8px rgba(0,0,0,0.1)' },
                  '100%': { boxShadow: '0 4px 16px rgba(255,255,255,0.3)' },
                },
                '&:hover': {
                  transform: 'translateY(-2px) scale(1.05)',
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.35) 0%, rgba(255,255,255,0.25) 100%)',
                },
              }}
            />
          </Box>
        </Container>
      </Box>

      {/* Main Header */}
      <AppBar
        position="fixed"
        sx={{
          background: scrolled
            ? 'linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(248,250,252,0.98) 50%, rgba(241,245,249,0.98) 100%)'
            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',
          color: '#1e293b',
          boxShadow: scrolled
            ? '0 12px 40px rgba(0,0,0,0.15), 0 4px 12px rgba(25,118,210,0.1), 0 2px 8px rgba(0,0,0,0.08)'
            : '0 8px 32px rgba(0,0,0,0.1), 0 4px 20px rgba(25,118,210,0.08), 0 2px 8px rgba(0,0,0,0.04)',
          zIndex: 1300,
          top: scrolled ? 0 : '52px',
          transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
          backdropFilter: scrolled ? 'blur(25px) saturate(200%)' : 'blur(15px) saturate(150%)',
          borderBottom: scrolled
            ? '1px solid rgba(25,118,210,0.15)'
            : '1px solid rgba(25,118,210,0.08)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '2px',
            background: scrolled
              ? 'linear-gradient(90deg, transparent 0%, rgba(25,118,210,0.8) 20%, rgba(255,152,0,0.6) 50%, rgba(25,118,210,0.8) 80%, transparent 100%)'
              : 'linear-gradient(90deg, transparent 0%, rgba(25,118,210,0.6) 25%, rgba(255,152,0,0.4) 50%, rgba(25,118,210,0.6) 75%, transparent 100%)',
            animation: scrolled ? 'headerShimmerScrolled 3s ease-in-out infinite' : 'headerShimmer 5s ease-in-out infinite',
            '@keyframes headerShimmer': {
              '0%, 100%': { opacity: 0.3, transform: 'scaleX(0.8)' },
              '50%': { opacity: 1, transform: 'scaleX(1.2)' },
            },
            '@keyframes headerShimmerScrolled': {
              '0%, 100%': { opacity: 0.6, transform: 'scaleX(1)' },
              '50%': { opacity: 1, transform: 'scaleX(1.1)' },
            },
          },
          '&::after': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: scrolled
              ? 'radial-gradient(ellipse at center top, rgba(25,118,210,0.03) 0%, transparent 50%)'
              : 'radial-gradient(ellipse at center top, rgba(25,118,210,0.05) 0%, rgba(255,152,0,0.02) 30%, transparent 60%)',
            pointerEvents: 'none',
            animation: 'headerGlow 6s ease-in-out infinite alternate',
            '@keyframes headerGlow': {
              '0%': { opacity: 0.5 },
              '100%': { opacity: 1 },
            },
          },
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative' }}>
          <Toolbar
            sx={{
              justifyContent: 'space-between',
              py: scrolled ? 0.8 : 1.5,
              px: { xs: 1, sm: 2 },
              transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
              position: 'relative',
              minHeight: scrolled ? '64px' : '80px',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: '-5px',
                left: '-60px',
                right: '-60px',
                bottom: '-5px',
                background: scrolled
                  ? 'linear-gradient(135deg, rgba(25,118,210,0.04) 0%, rgba(255,152,0,0.02) 30%, rgba(21,101,192,0.03) 70%, rgba(25,118,210,0.04) 100%)'
                  : 'linear-gradient(135deg, rgba(25,118,210,0.06) 0%, rgba(255,152,0,0.03) 25%, rgba(21,101,192,0.04) 50%, rgba(255,152,0,0.03) 75%, rgba(25,118,210,0.06) 100%)',
                borderRadius: scrolled ? '25px' : '30px',
                transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
                zIndex: -2,
                border: scrolled
                  ? '1px solid rgba(25,118,210,0.1)'
                  : '1px solid rgba(25,118,210,0.08)',
                boxShadow: scrolled
                  ? '0 4px 20px rgba(25,118,210,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
                  : '0 2px 15px rgba(25,118,210,0.08), inset 0 1px 0 rgba(255,255,255,0.15)',
              },
              '&::after': {
                content: '""',
                position: 'absolute',
                top: '50%',
                left: '50%',
                width: scrolled ? '85%' : '95%',
                height: scrolled ? '70%' : '80%',
                background: scrolled
                  ? 'radial-gradient(ellipse, rgba(25,118,210,0.06) 0%, rgba(255,152,0,0.02) 40%, transparent 70%)'
                  : 'radial-gradient(ellipse, rgba(25,118,210,0.08) 0%, rgba(255,152,0,0.03) 30%, transparent 70%)',
                borderRadius: '50%',
                transform: 'translate(-50%, -50%)',
                transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
                zIndex: -1,
                animation: 'advancedToolbarGlow 6s ease-in-out infinite alternate',
                '@keyframes advancedToolbarGlow': {
                  '0%': {
                    opacity: 0.4,
                    transform: 'translate(-50%, -50%) scale(0.9) rotate(0deg)',
                  },
                  '50%': {
                    opacity: 0.7,
                    transform: 'translate(-50%, -50%) scale(1.1) rotate(180deg)',
                  },
                  '100%': {
                    opacity: 0.5,
                    transform: 'translate(-50%, -50%) scale(1) rotate(360deg)',
                  },
                },
              },
            }}
          >
            {/* Logo */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: { xs: 1.5, sm: 2.5 },
                cursor: 'pointer',
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                position: 'relative',
                py: 1,
                px: 1.5,
                borderRadius: 3,
                '&:hover': {
                  transform: 'translateY(-3px) scale(1.03)',
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  width: '0%',
                  height: '0%',
                  background: 'radial-gradient(circle, rgba(25,118,210,0.15) 0%, rgba(255,152,0,0.08) 50%, transparent 80%)',
                  borderRadius: '50%',
                  transform: 'translate(-50%, -50%)',
                  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                  zIndex: -1,
                },
                '&:hover::before': {
                  width: '140%',
                  height: '140%',
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(25,118,210,0.05) 100%)',
                  borderRadius: 3,
                  opacity: 0,
                  transition: 'all 0.3s ease',
                  zIndex: -1,
                  border: '1px solid rgba(25,118,210,0.1)',
                },
                '&:hover::after': {
                  opacity: 1,
                  border: '1px solid rgba(25,118,210,0.2)',
                },
              }}
              onClick={() => navigate('/')}
            >
              <Box
                sx={{
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: scrolled ? '50px' : '65px',
                  height: scrolled ? '50px' : '65px',
                  borderRadius: '50%',
                  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: '-3px',
                    left: '-3px',
                    right: '-3px',
                    bottom: '-3px',
                    borderRadius: '50%',
                    background: 'conic-gradient(from 0deg, rgba(25,118,210,0.4) 0deg, rgba(255,152,0,0.6) 120deg, rgba(25,118,210,0.4) 240deg, rgba(255,152,0,0.6) 360deg)',
                    animation: 'logoRotate 12s linear infinite',
                    '@keyframes logoRotate': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' },
                    },
                    zIndex: 1,
                  },
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: '-1px',
                    left: '-1px',
                    right: '-1px',
                    bottom: '-1px',
                    borderRadius: '50%',
                    background: 'conic-gradient(from 180deg, transparent 0deg, rgba(25,118,210,0.2) 90deg, transparent 180deg, rgba(255,152,0,0.3) 270deg, transparent 360deg)',
                    animation: 'logoRotateReverse 8s linear infinite reverse',
                    '@keyframes logoRotateReverse': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(-360deg)' },
                    },
                    zIndex: 1,
                  },
                }}
              >
                <img
                  src="/ntss-logo.png"
                  alt="NTSS Logo"
                  style={{
                    height: scrolled ? '42px' : '55px',
                    width: scrolled ? '42px' : '55px',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    filter: scrolled
                      ? 'drop-shadow(0 4px 15px rgba(25,118,210,0.3)) brightness(1.1)'
                      : 'drop-shadow(0 6px 20px rgba(25,118,210,0.25)) brightness(1.05)',
                    position: 'relative',
                    zIndex: 3,
                    borderRadius: '50%',
                    objectFit: 'contain',
                  }}
                />
              </Box>
              <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 800,
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 50%, #0d47a1 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontSize: scrolled ? '1.1rem' : '1.3rem',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    lineHeight: 1.2,
                    textShadow: '0 2px 4px rgba(25,118,210,0.1)',
                    animation: 'textGlow 3s ease-in-out infinite alternate',
                    '@keyframes textGlow': {
                      '0%': { filter: 'brightness(1)' },
                      '100%': { filter: 'brightness(1.1)' },
                    },
                  }}
                >
                  Nyanza TSS
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: '#64748b',
                    fontSize: '0.75rem',
                    fontWeight: 600,
                    display: 'block',
                    lineHeight: 1,
                    letterSpacing: '0.5px',
                    textTransform: 'uppercase',
                    background: 'linear-gradient(135deg, #64748b 0%, #475569 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}
                >
                  TVET Excellence
                </Typography>
              </Box>
            </Box>

            {/* Desktop Navigation */}
            {!isMobile && (
              <Box
                sx={{
                  display: 'flex',
                  gap: 1.5,
                  alignItems: 'center',
                  position: 'relative',
                  px: 2,
                  py: 1,
                  borderRadius: 4,
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: '-12px',
                    left: '-25px',
                    right: '-25px',
                    bottom: '-12px',
                    background: scrolled
                      ? 'linear-gradient(135deg, rgba(25,118,210,0.04) 0%, rgba(255,152,0,0.03) 30%, rgba(25,118,210,0.04) 70%, rgba(255,152,0,0.03) 100%)'
                      : 'linear-gradient(135deg, rgba(25,118,210,0.03) 0%, rgba(255,152,0,0.02) 25%, rgba(25,118,210,0.03) 50%, rgba(255,152,0,0.02) 75%, rgba(25,118,210,0.03) 100%)',
                    borderRadius: '30px',
                    border: scrolled
                      ? '1px solid rgba(25,118,210,0.12)'
                      : '1px solid rgba(25,118,210,0.08)',
                    backdropFilter: 'blur(8px) saturate(120%)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    zIndex: -2,
                    boxShadow: scrolled
                      ? '0 4px 20px rgba(25,118,210,0.08), inset 0 1px 0 rgba(255,255,255,0.1)'
                      : '0 2px 15px rgba(25,118,210,0.06), inset 0 1px 0 rgba(255,255,255,0.08)',
                  },
                  '&:hover::before': {
                    background: 'linear-gradient(135deg, rgba(25,118,210,0.08) 0%, rgba(255,152,0,0.06) 25%, rgba(25,118,210,0.08) 50%, rgba(255,152,0,0.06) 75%, rgba(25,118,210,0.08) 100%)',
                    border: '1px solid rgba(25,118,210,0.2)',
                    boxShadow: '0 6px 30px rgba(25,118,210,0.12), inset 0 1px 0 rgba(255,255,255,0.15)',
                  },
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'linear-gradient(90deg, transparent 0%, rgba(255,152,0,0.05) 50%, transparent 100%)',
                    borderRadius: '30px',
                    opacity: 0,
                    transition: 'opacity 0.3s ease',
                    zIndex: -1,
                    animation: 'navGlow 4s ease-in-out infinite alternate',
                    '@keyframes navGlow': {
                      '0%': { opacity: 0 },
                      '100%': { opacity: 0.5 },
                    },
                  },
                }}
              >
                {/* Enhanced Floating particles effect */}
                {[...Array(8)].map((_, i) => (
                  <Box
                    key={i}
                    sx={{
                      position: 'absolute',
                      width: i % 2 === 0 ? '5px' : '3px',
                      height: i % 2 === 0 ? '5px' : '3px',
                      background: i % 3 === 0
                        ? 'linear-gradient(135deg, #1976d2, #42a5f5)'
                        : i % 3 === 1
                        ? 'linear-gradient(135deg, #ff9800, #ffb74d)'
                        : 'linear-gradient(135deg, #1976d2, #ff9800)',
                      borderRadius: '50%',
                      top: `${15 + (i * 8)}%`,
                      left: `${8 + (i * 12)}%`,
                      animation: `advancedFloat${i} ${2.5 + i * 0.4}s ease-in-out infinite`,
                      opacity: scrolled ? 0.4 : 0.7,
                      transition: 'opacity 0.3s ease',
                      filter: 'blur(0.5px)',
                      boxShadow: i % 2 === 0
                        ? '0 0 8px rgba(25,118,210,0.4)'
                        : '0 0 6px rgba(255,152,0,0.4)',
                      [`@keyframes advancedFloat${i}`]: {
                        '0%': {
                          transform: `translateY(0px) translateX(0px) rotate(0deg) scale(${0.8 + i * 0.1})`,
                          opacity: 0.3,
                        },
                        '25%': {
                          transform: `translateY(-${8 + i * 2}px) translateX(${4 + i}px) rotate(90deg) scale(${1 + i * 0.1})`,
                          opacity: 0.8,
                        },
                        '50%': {
                          transform: `translateY(-${12 + i * 3}px) translateX(0px) rotate(180deg) scale(${1.2 + i * 0.1})`,
                          opacity: 1,
                        },
                        '75%': {
                          transform: `translateY(-${8 + i * 2}px) translateX(-${4 + i}px) rotate(270deg) scale(${1 + i * 0.1})`,
                          opacity: 0.8,
                        },
                        '100%': {
                          transform: `translateY(0px) translateX(0px) rotate(360deg) scale(${0.8 + i * 0.1})`,
                          opacity: 0.3,
                        },
                      },
                    }}
                  />
                ))}

                {menuItems.map((item, index) => (
                  <Box
                    key={item.label}
                    sx={{
                      position: 'relative',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: '-5px',
                        left: '-5px',
                        right: '-5px',
                        bottom: '-5px',
                        background: `conic-gradient(from ${index * 60}deg, transparent 0deg, rgba(25,118,210,0.1) 90deg, transparent 180deg)`,
                        borderRadius: '15px',
                        opacity: 0,
                        transition: 'all 0.4s ease',
                        animation: `rotate${index} 8s linear infinite`,
                        zIndex: -1,
                        [`@keyframes rotate${index}`]: {
                          '0%': { transform: 'rotate(0deg)' },
                          '100%': { transform: 'rotate(360deg)' },
                        },
                      },
                      '&:hover::before': {
                        opacity: 1,
                      },
                    }}
                  >
                    <Button
                      onClick={(e) => item.submenu ? handleMenuClick(e, item.label) : handleNavigation(item.href, item.isRoute)}
                      endIcon={item.submenu ? <KeyboardArrowDown sx={{
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        transform: openSubmenu === item.label ? 'rotate(180deg) scale(1.2)' : 'rotate(0deg) scale(1)',
                      }} /> : null}
                      sx={{
                        color: '#1e293b',
                        fontWeight: 700,
                        fontSize: '0.95rem',
                        textTransform: 'none',
                        px: 4,
                        py: 2,
                        borderRadius: 5,
                        position: 'relative',
                        transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
                        overflow: 'hidden',
                        background: scrolled
                          ? 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 50%, rgba(241,245,249,0.9) 100%)'
                          : 'linear-gradient(135deg, rgba(255,255,255,0.85) 0%, rgba(248,250,252,0.85) 50%, rgba(241,245,249,0.85) 100%)',
                        backdropFilter: 'blur(15px) saturate(150%)',
                        border: scrolled
                          ? '1px solid rgba(25,118,210,0.15)'
                          : '1px solid rgba(25,118,210,0.12)',
                        boxShadow: scrolled
                          ? '0 4px 15px rgba(0,0,0,0.06), 0 2px 8px rgba(25,118,210,0.08)'
                          : '0 3px 12px rgba(0,0,0,0.05), 0 1px 6px rgba(25,118,210,0.06)',
                        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
                        letterSpacing: '-0.01em',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 30%, #ff9800 70%, #0d47a1 100%)',
                          color: 'white',
                          transform: 'translateY(-5px) scale(1.08)',
                          boxShadow: '0 15px 40px rgba(25, 118, 210, 0.4), 0 8px 20px rgba(255, 152, 0, 0.2), 0 4px 12px rgba(25, 118, 210, 0.3)',
                          border: '1px solid rgba(255,152,0,0.4)',
                          textShadow: '0 2px 4px rgba(0,0,0,0.2)',
                        },
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: '-120%',
                          width: '100%',
                          height: '100%',
                          background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 30%, rgba(255,152,0,0.3) 50%, rgba(255,255,255,0.4) 70%, transparent 100%)',
                          transition: 'left 1s cubic-bezier(0.4, 0, 0.2, 1)',
                          borderRadius: 5,
                        },
                        '&:hover::before': {
                          left: '120%',
                        },
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: '50%',
                          width: 0,
                          height: '5px',
                          background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 25%, #ef6c00 50%, #e65100 75%, #ff9800 100%)',
                          borderRadius: '4px 4px 0 0',
                          transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
                          transform: 'translateX(-50%)',
                          boxShadow: '0 3px 12px rgba(255,152,0,0.5)',
                          backgroundSize: '200% 100%',
                          animation: 'gradientShift 3s ease-in-out infinite',
                          '@keyframes gradientShift': {
                            '0%, 100%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' },
                          },
                        },
                        '&:hover::after': {
                          width: '100%',
                          height: '6px',
                          boxShadow: '0 6px 20px rgba(255,152,0,0.7), 0 3px 10px rgba(255,152,0,0.5)',
                          animation: 'none',
                          background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 25%, #ef6c00 50%, #e65100 75%, #ff9800 100%)',
                        },
                      }}
                    >
                      <Box
                        sx={{
                          position: 'relative',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            left: '-8px',
                            top: '50%',
                            width: '3px',
                            height: '3px',
                            background: 'currentColor',
                            borderRadius: '50%',
                            transform: 'translateY(-50%)',
                            opacity: 0,
                            transition: 'all 0.3s ease',
                          },
                          '&:hover::before': {
                            opacity: 1,
                            animation: 'dotPulse 1s ease-in-out infinite',
                            '@keyframes dotPulse': {
                              '0%, 100%': { transform: 'translateY(-50%) scale(1)' },
                              '50%': { transform: 'translateY(-50%) scale(1.5)' },
                            },
                          },
                        }}
                      >
                        {item.label}
                      </Box>
                    </Button>
                  {item.submenu && (
                    <Menu
                      anchorEl={anchorEl}
                      open={Boolean(anchorEl) && openSubmenu === item.label}
                      onClose={handleMenuClose}
                      sx={{
                        '& .MuiPaper-root': {
                          mt: 2,
                          minWidth: 250,
                          background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%)',
                          backdropFilter: 'blur(20px) saturate(180%)',
                          borderRadius: 3,
                          border: '1px solid rgba(25,118,210,0.15)',
                          boxShadow: '0 20px 60px rgba(0,0,0,0.15), 0 8px 25px rgba(25,118,210,0.1)',
                          overflow: 'hidden',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            height: '2px',
                            background: 'linear-gradient(90deg, #1976d2 0%, #ff9800 50%, #1976d2 100%)',
                            animation: 'menuShimmer 3s ease-in-out infinite',
                            '@keyframes menuShimmer': {
                              '0%, 100%': { opacity: 0.5 },
                              '50%': { opacity: 1 },
                            },
                          },
                        }
                      }}
                    >
                      {item.submenu.map((subItem, subIndex) => (
                        <MenuItem
                          key={subItem.label}
                          onClick={() => handleNavigation(subItem.href, subItem.isRoute)}
                          sx={{
                            fontSize: '0.95rem',
                            fontWeight: 600,
                            py: 2,
                            px: 3,
                            position: 'relative',
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            borderRadius: 2,
                            mx: 1,
                            my: 0.5,
                            animation: `slideIn 0.3s ease-out ${subIndex * 0.1}s both`,
                            '@keyframes slideIn': {
                              '0%': { opacity: 0, transform: 'translateX(-20px)' },
                              '100%': { opacity: 1, transform: 'translateX(0)' },
                            },
                            '&:hover': {
                              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                              color: 'white',
                              transform: 'translateX(8px) scale(1.02)',
                              boxShadow: '0 8px 25px rgba(25,118,210,0.3)',
                            },
                            '&::before': {
                              content: '""',
                              position: 'absolute',
                              left: 0,
                              top: '50%',
                              width: '3px',
                              height: 0,
                              background: 'linear-gradient(135deg, #ff9800, #f57c00)',
                              borderRadius: '0 2px 2px 0',
                              transform: 'translateY(-50%)',
                              transition: 'height 0.3s ease',
                            },
                            '&:hover::before': {
                              height: '70%',
                            },
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 2,
                              '&::before': {
                                content: '""',
                                width: '6px',
                                height: '6px',
                                background: 'currentColor',
                                borderRadius: '50%',
                                opacity: 0.7,
                                transition: 'all 0.3s ease',
                              },
                              '&:hover::before': {
                                transform: 'scale(1.5)',
                                opacity: 1,
                              },
                            }}
                          >
                            {subItem.label}
                          </Box>
                        </MenuItem>
                      ))}
                    </Menu>
                  )}
                </Box>
              ))}
                <Button
                  variant="contained"
                  sx={{
                    ml: 4,
                    background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 25%, #ef6c00 50%, #e65100 75%, #ff9800 100%)',
                    color: 'white',
                    textTransform: 'none',
                    fontWeight: 800,
                    fontSize: '1rem',
                    px: 5,
                    py: 2,
                    borderRadius: 6,
                    position: 'relative',
                    overflow: 'hidden',
                    boxShadow: scrolled
                      ? '0 6px 20px rgba(255, 152, 0, 0.5), 0 3px 12px rgba(255, 152, 0, 0.3), 0 1px 6px rgba(255, 152, 0, 0.2)'
                      : '0 5px 18px rgba(255, 152, 0, 0.4), 0 2px 10px rgba(255, 152, 0, 0.25), 0 1px 5px rgba(255, 152, 0, 0.15)',
                    transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
                    backgroundSize: '200% 100%',
                    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
                    letterSpacing: '-0.01em',
                    border: '2px solid rgba(255,152,0,0.3)',
                    animation: 'advancedButtonPulse 4s ease-in-out infinite',
                    '@keyframes advancedButtonPulse': {
                      '0%, 100%': {
                        boxShadow: '0 5px 18px rgba(255, 152, 0, 0.4), 0 2px 10px rgba(255, 152, 0, 0.25)',
                        transform: 'scale(1)',
                        backgroundPosition: '0% 50%',
                      },
                      '25%': {
                        boxShadow: '0 7px 25px rgba(255, 152, 0, 0.6), 0 4px 15px rgba(255, 152, 0, 0.4)',
                        transform: 'scale(1.03)',
                        backgroundPosition: '50% 50%',
                      },
                      '50%': {
                        boxShadow: '0 8px 30px rgba(255, 152, 0, 0.7), 0 5px 18px rgba(255, 152, 0, 0.5)',
                        transform: 'scale(1.05)',
                        backgroundPosition: '100% 50%',
                      },
                      '75%': {
                        boxShadow: '0 7px 25px rgba(255, 152, 0, 0.6), 0 4px 15px rgba(255, 152, 0, 0.4)',
                        transform: 'scale(1.03)',
                        backgroundPosition: '50% 50%',
                      },
                    },
                    '&:hover': {
                      background: 'linear-gradient(135deg, #e65100 0%, #d84315 25%, #bf360c 50%, #ff5722 75%, #ff9800 100%)',
                      transform: 'translateY(-4px) scale(1.08)',
                      boxShadow: '0 12px 40px rgba(255, 152, 0, 0.6), 0 8px 25px rgba(255, 87, 34, 0.4), 0 4px 15px rgba(255, 152, 0, 0.5)',
                      animation: 'none',
                      border: '2px solid rgba(255,87,34,0.6)',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: '-120%',
                      width: '100%',
                      height: '100%',
                      background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 25%, rgba(255,193,7,0.3) 50%, rgba(255,255,255,0.4) 75%, transparent 100%)',
                      transition: 'left 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
                      borderRadius: 6,
                    },
                    '&:hover::before': {
                      left: '120%',
                    },
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      top: '50%',
                      right: '18px',
                      width: '8px',
                      height: '8px',
                      background: 'radial-gradient(circle, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.6) 70%, transparent 100%)',
                      borderRadius: '50%',
                      transform: 'translateY(-50%)',
                      boxShadow: '0 0 12px rgba(255,255,255,0.6), 0 0 6px rgba(255,255,255,0.4)',
                      animation: 'advancedDot 3s ease-in-out infinite',
                      '@keyframes advancedDot': {
                        '0%, 100%': {
                          opacity: 0.4,
                          transform: 'translateY(-50%) scale(0.8)',
                          boxShadow: '0 0 8px rgba(255,255,255,0.4), 0 0 4px rgba(255,255,255,0.2)',
                        },
                        '25%': {
                          opacity: 0.8,
                          transform: 'translateY(-50%) scale(1.1)',
                          boxShadow: '0 0 15px rgba(255,255,255,0.7), 0 0 8px rgba(255,255,255,0.5)',
                        },
                        '50%': {
                          opacity: 1,
                          transform: 'translateY(-50%) scale(1.3)',
                          boxShadow: '0 0 20px rgba(255,255,255,0.8), 0 0 12px rgba(255,255,255,0.6)',
                        },
                        '75%': {
                          opacity: 0.8,
                          transform: 'translateY(-50%) scale(1.1)',
                          boxShadow: '0 0 15px rgba(255,255,255,0.7), 0 0 8px rgba(255,255,255,0.5)',
                        },
                      },
                    },
                  }}
                  onClick={() => navigate('/admissions')}
                >
                  Apply Now
                </Button>
              </Box>
            )}

            {/* Mobile Menu Button */}
            {isMobile && (
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{
                  background: scrolled
                    ? 'linear-gradient(135deg, rgba(25, 118, 210, 0.15) 0%, rgba(255, 152, 0, 0.08) 50%, rgba(21, 101, 192, 0.15) 100%)'
                    : 'linear-gradient(135deg, rgba(25, 118, 210, 0.12) 0%, rgba(255, 152, 0, 0.06) 50%, rgba(21, 101, 192, 0.12) 100%)',
                  borderRadius: 4,
                  border: scrolled
                    ? '2px solid rgba(25, 118, 210, 0.25)'
                    : '2px solid rgba(25, 118, 210, 0.2)',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
                  position: 'relative',
                  overflow: 'hidden',
                  backdropFilter: 'blur(10px) saturate(120%)',
                  boxShadow: scrolled
                    ? '0 4px 15px rgba(25, 118, 210, 0.2), 0 2px 8px rgba(255, 152, 0, 0.1)'
                    : '0 3px 12px rgba(25, 118, 210, 0.15), 0 1px 6px rgba(255, 152, 0, 0.08)',
                  width: '48px',
                  height: '48px',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #1976d2 0%, #ff9800 30%, #1565c0 70%, #0d47a1 100%)',
                    color: 'white',
                    transform: 'translateY(-3px) rotate(180deg) scale(1.15)',
                    boxShadow: '0 12px 35px rgba(25, 118, 210, 0.5), 0 6px 20px rgba(255, 152, 0, 0.3)',
                    border: '2px solid rgba(255, 152, 0, 0.6)',
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: '-120%',
                    width: '100%',
                    height: '100%',
                    background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 30%, rgba(255,152,0,0.2) 50%, rgba(255,255,255,0.3) 70%, transparent 100%)',
                    transition: 'left 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
                    borderRadius: 4,
                  },
                  '&:hover::before': {
                    left: '120%',
                  },
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    width: '0%',
                    height: '0%',
                    background: 'radial-gradient(circle, rgba(255,152,0,0.3) 0%, transparent 70%)',
                    borderRadius: '50%',
                    transform: 'translate(-50%, -50%)',
                    transition: 'all 0.4s ease',
                    zIndex: -1,
                  },
                  '&:hover::after': {
                    width: '120%',
                    height: '120%',
                  },
                }}
              >
                <MenuIcon sx={{
                  fontSize: '1.6rem',
                  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
                  position: 'relative',
                  zIndex: 2,
                  '&:hover': {
                    filter: 'drop-shadow(0 4px 8px rgba(255,255,255,0.3))',
                  },
                }} />
              </IconButton>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: 320,
            background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 50%, #0d47a1 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255,152,0,0.1) 0%, transparent 50%)
              `,
              pointerEvents: 'none',
            },
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '2px',
              background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.5) 50%, transparent 100%)',
              animation: 'drawerShimmer 3s ease-in-out infinite',
              '@keyframes drawerShimmer': {
                '0%, 100%': { opacity: 0 },
                '50%': { opacity: 1 },
              },
            },
          },
          '& .MuiBackdrop-root': {
            backgroundColor: 'rgba(0,0,0,0.6)',
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Spacer for fixed header */}
      <Box sx={{ height: scrolled ? '70px' : '118px', transition: 'height 0.3s ease' }} />
    </>
  )
}

export default Header
