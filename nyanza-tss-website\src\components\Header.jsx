import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  AppBar,
  <PERSON><PERSON><PERSON>,
  Typography,
  Button,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Box,
  useTheme,
  useMediaQuery,
  Menu,
  MenuItem,
  Collapse,
  Container,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  ExpandMore,
  ExpandLess,
  Phone,
  Email,
  LocationOn,
  KeyboardArrowDown,
} from '@mui/icons-material'

const Header = () => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const [openSubmenu, setOpenSubmenu] = useState('')
  const [scrolled, setScrolled] = useState(false)
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'))

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50
      setScrolled(isScrolled)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navigate = useNavigate()

  const menuItems = [
    {
      label: 'Our School',
      submenu: [
        { label: 'About Nyanza TSS', href: '/about', isRoute: true },
        { label: "Principal's Message", href: '/principal', isRoute: true },
        { label: 'Facilities', href: '/facilities', isRoute: true },
        { label: 'Work With Us', href: '/careers', isRoute: true },
      ]
    },
    {
      label: 'Admissions',
      submenu: [
        { label: 'Admissions', href: '/admissions', isRoute: true },
        { label: 'School Fees Structure', href: '/fees', isRoute: true },
      ]
    },
    {
      label: 'Academic Life',
      submenu: [
        { label: 'Academic Staff', href: '/academic-staff', isRoute: true },
        { label: 'Computer Science', href: '/computer-science', isRoute: true },
        { label: 'Technical Skills', href: '/technical-skills', isRoute: true },
      ]
    },
    {
      label: 'Student Life',
      submenu: [
        { label: 'Discover Nyanza TSS', href: '/discover', isRoute: true },
        { label: 'School Calendar', href: '/school-calendar', isRoute: true },
        { label: 'Clubs & Societies', href: '/clubs-societies', isRoute: true },
        { label: 'Sports at Nyanza TSS', href: '/sports', isRoute: true },
        { label: 'Students Handbook', href: '/students-handbook', isRoute: true },
      ]
    },
    { label: 'Contacts', href: '/contacts', isRoute: true },
  ]

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleMenuClick = (event, menuLabel) => {
    if (isMobile) {
      setOpenSubmenu(openSubmenu === menuLabel ? '' : menuLabel)
    } else {
      setAnchorEl(event.currentTarget)
      setOpenSubmenu(menuLabel)
    }
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setOpenSubmenu('')
  }

  const handleNavigation = (href, isRoute = false) => {
    if (isRoute) {
      navigate(href)
    } else {
      const element = document.querySelector(href)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    setMobileOpen(false)
    handleMenuClose()
  }

  const drawer = (
    <Box sx={{ width: 300, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Drawer Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 3,
          borderBottom: '1px solid rgba(255,255,255,0.2)',
          background: 'rgba(255,255,255,0.1)',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <img
            src="/ntss-logo.png"
            alt="NTSS Logo"
            style={{
              height: '45px',
              filter: 'brightness(0) invert(1)',
            }}
          />
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'white' }}>
              Nyanza TSS
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)' }}>
              TVET Excellence
            </Typography>
          </Box>
        </Box>
        <IconButton
          onClick={handleDrawerToggle}
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255,255,255,0.1)',
              transform: 'rotate(90deg)',
            },
            transition: 'all 0.3s ease',
          }}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      {/* Navigation Menu */}
      <Box sx={{ flex: 1, py: 2 }}>
        <List sx={{ px: 1 }}>
          {menuItems.map((item) => (
            <Box key={item.label}>
              <ListItem
                button
                onClick={() => item.submenu ? handleMenuClick(null, item.label) : handleNavigation(item.href, item.isRoute)}
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  mx: 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    transform: 'translateX(8px)',
                  }
                }}
              >
                <Box
                  sx={{
                    width: 4,
                    height: 4,
                    borderRadius: '50%',
                    backgroundColor: '#ff9800',
                    mr: 2,
                  }}
                />
                <ListItemText
                  primary={item.label}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontWeight: 600,
                      fontSize: '1rem',
                    }
                  }}
                />
                {item.submenu && (
                  <Box sx={{ color: 'rgba(255,255,255,0.7)' }}>
                    {openSubmenu === item.label ? <ExpandLess /> : <ExpandMore />}
                  </Box>
                )}
              </ListItem>
              {item.submenu && (
                <Collapse in={openSubmenu === item.label} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.submenu.map((subItem) => (
                      <ListItem
                        key={subItem.label}
                        button
                        sx={{
                          pl: 6,
                          borderRadius: 2,
                          mx: 2,
                          mb: 0.5,
                          '&:hover': {
                            backgroundColor: 'rgba(255,255,255,0.1)',
                          }
                        }}
                        onClick={() => handleNavigation(subItem.href, subItem.isRoute)}
                      >
                        <ListItemText
                          primary={subItem.label}
                          sx={{
                            '& .MuiListItemText-primary': {
                              fontSize: '0.9rem',
                              color: 'rgba(255,255,255,0.9)',
                            }
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              )}
            </Box>
          ))}
        </List>
      </Box>

      {/* Drawer Footer */}
      <Box
        sx={{
          p: 3,
          borderTop: '1px solid rgba(255,255,255,0.2)',
          background: 'rgba(255,255,255,0.1)',
        }}
      >
        <Button
          variant="contained"
          fullWidth
          sx={{
            backgroundColor: '#ff9800',
            color: 'white',
            fontWeight: 600,
            py: 1.5,
            borderRadius: 2,
            '&:hover': {
              backgroundColor: '#f57c00',
            }
          }}
        >
          Apply Now
        </Button>
      </Box>
    </Box>
  )

  return (
    <>
      {/* Professional Top Contact Bar */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #1d4ed8 100%)',
          color: 'white',
          py: 1,
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1301,
          transform: scrolled ? 'translateY(-100%)' : 'translateY(0)',
          transition: 'all 0.3s ease-in-out',
          boxShadow: '0 2px 8px rgba(30, 58, 138, 0.2)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.08)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.05) 50%, transparent 100%)',
            opacity: 0.8,
          },
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 2,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'wrap' }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    filter: 'brightness(1.2)',
                  }
                }}
              >
                <Phone sx={{
                  fontSize: 16,
                  opacity: 0.9,
                }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                  +250 788 309 436
                </Typography>
              </Box>
              <Box
                sx={{
                  display: { xs: 'none', sm: 'flex' },
                  alignItems: 'center',
                  gap: 0.5,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    opacity: 0.8,
                  }
                }}
              >
                <Email sx={{
                  fontSize: 16,
                  opacity: 0.9,
                }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                  <EMAIL>
                </Typography>
              </Box>
              <Box
                sx={{
                  display: { xs: 'none', md: 'flex' },
                  alignItems: 'center',
                  gap: 0.5,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    opacity: 0.8,
                  }
                }}
              >
                <LocationOn sx={{
                  fontSize: 16,
                  opacity: 0.9,
                }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem', fontWeight: 500 }}>
                  Kigoma Sector, Nyanza District
                </Typography>
              </Box>
            </Box>
            <Chip
              label="Excellence in TVET Education"
              size="small"
              sx={{
                background: 'rgba(255,255,255,0.15)',
                color: 'white',
                fontWeight: 600,
                display: { xs: 'none', md: 'flex' },
                border: '1px solid rgba(255,255,255,0.2)',
                transition: 'all 0.2s ease',
                '&:hover': {
                  background: 'rgba(255,255,255,0.25)',
                },
              }}
            />
          </Box>
        </Container>
      </Box>

      {/* Main Header */}
      <AppBar
        position="fixed"
        sx={{
          background: scrolled
            ? 'rgba(255,255,255,0.95)'
            : '#ffffff',
          color: '#1e293b',
          boxShadow: scrolled
            ? '0 4px 20px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.1)'
            : '0 2px 10px rgba(0,0,0,0.05)',
          zIndex: 1300,
          top: scrolled ? 0 : '40px',
          transition: 'all 0.3s ease-in-out',
          backdropFilter: scrolled ? 'blur(20px) saturate(180%)' : 'none',
          borderBottom: scrolled
            ? '1px solid rgba(0,0,0,0.08)'
            : '1px solid rgba(0,0,0,0.05)',


        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative' }}>
          <Toolbar
            sx={{
              justifyContent: 'space-between',
              py: scrolled ? 1 : 1.5,
              px: { xs: 1, sm: 2 },
              transition: 'all 0.3s ease-in-out',
              position: 'relative',
              minHeight: scrolled ? '64px' : '72px',


            }}
          >
            {/* Logo */}
            <Box
              onClick={() => navigate('/')}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: { xs: 1.5, sm: 2.5 },
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                '&:hover': {
                  opacity: 0.8,
                },
              }}
            >
              <Box
                sx={{
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: scrolled ? '45px' : '50px',
                  height: scrolled ? '45px' : '50px',
                  borderRadius: '50%',
                  transition: 'all 0.2s ease',
                  border: '2px solid rgba(30, 58, 138, 0.1)',

                }}
              >
                <img
                  src="/ntss-logo.png"
                  alt="NTSS Logo"
                  style={{
                    height: scrolled ? '38px' : '42px',
                    width: scrolled ? '38px' : '42px',
                    transition: 'all 0.2s ease',
                    borderRadius: '50%',
                    objectFit: 'contain',
                  }}
                />
              </Box>
              <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    color: '#1e3a8a',
                    fontSize: scrolled ? '1.1rem' : '1.25rem',
                    transition: 'all 0.2s ease',
                    lineHeight: 1.2,
                    letterSpacing: '0.3px',
                  }}
                >
                  Nyanza TSS
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: '#64748b',
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    display: 'block',
                    lineHeight: 1,
                    letterSpacing: '0.5px',
                    textTransform: 'uppercase',
                  }}
                >
                  TVET Excellence
                </Typography>
              </Box>
            </Box>

            {/* Desktop Navigation */}
            {!isMobile && (
              <Box
                sx={{
                  display: 'flex',
                  gap: 1.5,
                  alignItems: 'center',
                }}
              >


                {menuItems.map((item) => (
                  <Box key={item.label}>
                    <Button
                      onClick={(e) => item.submenu ? handleMenuClick(e, item.label) : handleNavigation(item.href, item.isRoute)}
                      endIcon={item.submenu ? <KeyboardArrowDown sx={{
                        transition: 'all 0.2s ease',
                        transform: openSubmenu === item.label ? 'rotate(180deg)' : 'rotate(0deg)',
                      }} /> : null}
                      sx={{
                        color: '#1e293b',
                        fontWeight: 500,
                        fontSize: '0.9rem',
                        textTransform: 'none',
                        px: 2.5,
                        py: 1,
                        borderRadius: 2,
                        transition: 'all 0.2s ease',
                        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
                        '&:hover': {
                          backgroundColor: 'rgba(30, 58, 138, 0.08)',
                          color: '#1e3a8a',
                        },


                      }}
                    >
                      {item.label}
                    </Button>
                  {item.submenu && (
                    <Menu
                      anchorEl={anchorEl}
                      open={Boolean(anchorEl) && openSubmenu === item.label}
                      onClose={handleMenuClose}
                      sx={{
                        '& .MuiPaper-root': {
                          mt: 1,
                          minWidth: 200,
                          backgroundColor: '#ffffff',
                          borderRadius: 2,
                          border: '1px solid rgba(0,0,0,0.1)',
                          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                        }
                      }}
                    >
                      {item.submenu.map((subItem, subIndex) => (
                        <MenuItem
                          key={subItem.label}
                          onClick={() => handleNavigation(subItem.href, subItem.isRoute)}
                          sx={{
                            fontSize: '0.9rem',
                            fontWeight: 400,
                            py: 1.5,
                            px: 2,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              backgroundColor: 'rgba(30, 58, 138, 0.08)',
                              color: '#1e3a8a',
                            },
                          }}
                        >
                          {subItem.label}
                        </MenuItem>
                      ))}
                    </Menu>
                  )}
                </Box>
              ))}
                <Button
                  variant="contained"
                  sx={{
                    ml: 3,
                    backgroundColor: '#1e3a8a',
                    color: 'white',
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: '0.9rem',
                    px: 3,
                    py: 1.5,
                    borderRadius: 2,
                    transition: 'all 0.2s ease',
                    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
                    '&:hover': {
                      backgroundColor: '#1e40af',
                    },
                  }}
                  onClick={() => navigate('/admissions')}
                >
                  Apply Now
                </Button>
              </Box>
            )}

            {/* Professional Mobile Menu Button */}
            {isMobile && (
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{
                  color: '#1e3a8a',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    backgroundColor: 'rgba(30, 58, 138, 0.08)',
                  },
                }}
              >
                <MenuIcon />
              </IconButton>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: 320,
            background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 50%, #0d47a1 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255,152,0,0.1) 0%, transparent 50%)
              `,
              pointerEvents: 'none',
            },
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '2px',
              background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.5) 50%, transparent 100%)',
              animation: 'drawerShimmer 3s ease-in-out infinite',
              '@keyframes drawerShimmer': {
                '0%, 100%': { opacity: 0 },
                '50%': { opacity: 1 },
              },
            },
          },
          '& .MuiBackdrop-root': {
            backgroundColor: 'rgba(0,0,0,0.6)',
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Spacer for fixed header */}
      <Box sx={{ height: scrolled ? '70px' : '118px', transition: 'height 0.3s ease' }} />
    </>
  )
}

export default Header
