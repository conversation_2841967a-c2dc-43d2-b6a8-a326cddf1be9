import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON>pography,
  <PERSON>ton,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Box,
  useTheme,
  useMediaQuery,
  Menu,
  MenuItem,
  Collapse,
  Container,
  Fade,
  Slide,
  Paper,
  Divider,
  Chip,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  ExpandMore,
  ExpandLess,
  Phone,
  Email,
  LocationOn,
  KeyboardArrowDown,
  School,
} from '@mui/icons-material'

const Header = () => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const [openSubmenu, setOpenSubmenu] = useState('')
  const [scrolled, setScrolled] = useState(false)
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'))

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50
      setScrolled(isScrolled)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navigate = useNavigate()

  const menuItems = [
    {
      label: 'Our School',
      submenu: [
        { label: 'About Nyanza TSS', href: '/about', isRoute: true },
        { label: "Principal's Message", href: '/principal', isRoute: true },
        { label: 'Facilities', href: '/facilities', isRoute: true },
        { label: 'Work With Us', href: '/careers', isRoute: true },
      ]
    },
    {
      label: 'Admissions',
      submenu: [
        { label: 'Admissions', href: '/admissions', isRoute: true },
        { label: 'School Fees Structure', href: '/fees', isRoute: true },
      ]
    },
    {
      label: 'Academic Life',
      submenu: [
        { label: 'Academic Staff', href: '/academic-staff', isRoute: true },
        { label: 'Computer Science', href: '/computer-science', isRoute: true },
        { label: 'Technical Skills', href: '/technical-skills', isRoute: true },
      ]
    },
    {
      label: 'Student Life',
      submenu: [
        { label: 'Discover Nyanza TSS', href: '/discover', isRoute: true },
        { label: 'School Calendar', href: '/school-calendar', isRoute: true },
        { label: 'Clubs & Societies', href: '/clubs-societies', isRoute: true },
        { label: 'Sports at Nyanza TSS', href: '/sports', isRoute: true },
        { label: 'Students Handbook', href: '/students-handbook', isRoute: true },
      ]
    },
    { label: 'Contacts', href: '/contacts', isRoute: true },
  ]

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleMenuClick = (event, menuLabel) => {
    if (isMobile) {
      setOpenSubmenu(openSubmenu === menuLabel ? '' : menuLabel)
    } else {
      setAnchorEl(event.currentTarget)
      setOpenSubmenu(menuLabel)
    }
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setOpenSubmenu('')
  }

  const handleNavigation = (href, isRoute = false) => {
    if (isRoute) {
      navigate(href)
    } else {
      const element = document.querySelector(href)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    setMobileOpen(false)
    handleMenuClose()
  }

  const drawer = (
    <Box sx={{ width: 300, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Drawer Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 3,
          borderBottom: '1px solid rgba(255,255,255,0.2)',
          background: 'rgba(255,255,255,0.1)',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <img
            src="/ntss-logo.png"
            alt="NTSS Logo"
            style={{
              height: '45px',
              filter: 'brightness(0) invert(1)',
            }}
          />
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'white' }}>
              Nyanza TSS
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)' }}>
              TVET Excellence
            </Typography>
          </Box>
        </Box>
        <IconButton
          onClick={handleDrawerToggle}
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255,255,255,0.1)',
              transform: 'rotate(90deg)',
            },
            transition: 'all 0.3s ease',
          }}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      {/* Navigation Menu */}
      <Box sx={{ flex: 1, py: 2 }}>
        <List sx={{ px: 1 }}>
          {menuItems.map((item) => (
            <Box key={item.label}>
              <ListItem
                button
                onClick={() => item.submenu ? handleMenuClick(null, item.label) : handleNavigation(item.href, item.isRoute)}
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  mx: 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    transform: 'translateX(8px)',
                  }
                }}
              >
                <Box
                  sx={{
                    width: 4,
                    height: 4,
                    borderRadius: '50%',
                    backgroundColor: '#ff9800',
                    mr: 2,
                  }}
                />
                <ListItemText
                  primary={item.label}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontWeight: 600,
                      fontSize: '1rem',
                    }
                  }}
                />
                {item.submenu && (
                  <Box sx={{ color: 'rgba(255,255,255,0.7)' }}>
                    {openSubmenu === item.label ? <ExpandLess /> : <ExpandMore />}
                  </Box>
                )}
              </ListItem>
              {item.submenu && (
                <Collapse in={openSubmenu === item.label} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.submenu.map((subItem) => (
                      <ListItem
                        key={subItem.label}
                        button
                        sx={{
                          pl: 6,
                          borderRadius: 2,
                          mx: 2,
                          mb: 0.5,
                          '&:hover': {
                            backgroundColor: 'rgba(255,255,255,0.1)',
                          }
                        }}
                        onClick={() => handleNavigation(subItem.href, subItem.isRoute)}
                      >
                        <ListItemText
                          primary={subItem.label}
                          sx={{
                            '& .MuiListItemText-primary': {
                              fontSize: '0.9rem',
                              color: 'rgba(255,255,255,0.9)',
                            }
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              )}
            </Box>
          ))}
        </List>
      </Box>

      {/* Drawer Footer */}
      <Box
        sx={{
          p: 3,
          borderTop: '1px solid rgba(255,255,255,0.2)',
          background: 'rgba(255,255,255,0.1)',
        }}
      >
        <Button
          variant="contained"
          fullWidth
          sx={{
            backgroundColor: '#ff9800',
            color: 'white',
            fontWeight: 600,
            py: 1.5,
            borderRadius: 2,
            '&:hover': {
              backgroundColor: '#f57c00',
            }
          }}
        >
          Apply Now
        </Button>
      </Box>
    </Box>
  )

  return (
    <>
      {/* Top Contact Bar */}
      <Box
        sx={{
          backgroundColor: '#1976d2',
          color: 'white',
          py: 1,
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1301,
          transform: scrolled ? 'translateY(-100%)' : 'translateY(0)',
          transition: 'transform 0.3s ease-in-out',
        }}
      >
        <Container maxWidth="lg">
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 2,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'wrap' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Phone sx={{ fontSize: 16 }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                  +250 788 309 436
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Email sx={{ fontSize: 16 }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                  <EMAIL>
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <LocationOn sx={{ fontSize: 16 }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                  Kigoma Sector, Nyanza District
                </Typography>
              </Box>
            </Box>
            <Chip
              label="Excellence in TVET Education"
              size="small"
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
                fontWeight: 500,
                display: { xs: 'none', md: 'flex' },
              }}
            />
          </Box>
        </Container>
      </Box>

      {/* Main Header */}
      <AppBar
        position="fixed"
        sx={{
          backgroundColor: scrolled ? 'rgba(255,255,255,0.95)' : 'white',
          color: '#333',
          boxShadow: scrolled
            ? '0 4px 20px rgba(0,0,0,0.15)'
            : '0 2px 10px rgba(0,0,0,0.1)',
          zIndex: 1300,
          top: scrolled ? 0 : '48px',
          transition: 'all 0.3s ease-in-out',
          backdropFilter: scrolled ? 'blur(10px)' : 'none',
        }}
      >
        <Container maxWidth="lg">
          <Toolbar sx={{ justifyContent: 'space-between', py: scrolled ? 0.5 : 1, transition: 'padding 0.3s ease' }}>
            {/* Logo */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                cursor: 'pointer',
                transition: 'transform 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.05)',
                }
              }}
              onClick={() => navigate('/')}
            >
              <img
                src="/ntss-logo.png"
                alt="NTSS Logo"
                style={{
                  height: scrolled ? '45px' : '55px',
                  transition: 'height 0.3s ease',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                }}
              />
              <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 'bold',
                    color: '#1976d2',
                    fontSize: scrolled ? '1.1rem' : '1.3rem',
                    transition: 'font-size 0.3s ease',
                    lineHeight: 1.2,
                  }}
                >
                  Nyanza TSS
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: '#666',
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    display: 'block',
                    lineHeight: 1,
                  }}
                >
                  TVET Excellence
                </Typography>
              </Box>
            </Box>

            {/* Desktop Navigation */}
            {!isMobile && (
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                {menuItems.map((item) => (
                  <Box key={item.label}>
                    <Button
                      onClick={(e) => item.submenu ? handleMenuClick(e, item.label) : handleNavigation(item.href, item.isRoute)}
                      endIcon={item.submenu ? <KeyboardArrowDown /> : null}
                      sx={{
                        color: '#333',
                        fontWeight: 600,
                        fontSize: '0.95rem',
                        textTransform: 'none',
                        px: 2.5,
                        py: 1.2,
                        borderRadius: 2,
                        position: 'relative',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          backgroundColor: '#1976d2',
                          color: 'white',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                        },
                        '&:before': {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: '50%',
                          width: 0,
                          height: '2px',
                          backgroundColor: '#1976d2',
                          transition: 'all 0.3s ease',
                          transform: 'translateX(-50%)',
                        },
                        '&:hover:before': {
                          width: '80%',
                        },
                      }}
                    >
                      {item.label}
                    </Button>
                  {item.submenu && (
                    <Menu
                      anchorEl={anchorEl}
                      open={Boolean(anchorEl) && openSubmenu === item.label}
                      onClose={handleMenuClose}
                      sx={{
                        '& .MuiPaper-root': {
                          mt: 1,
                          minWidth: 200,
                          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                        }
                      }}
                    >
                      {item.submenu.map((subItem) => (
                        <MenuItem
                          key={subItem.label}
                          onClick={() => handleNavigation(subItem.href, subItem.isRoute)}
                          sx={{
                            fontSize: '0.9rem',
                            '&:hover': {
                              backgroundColor: '#1976d2',
                              color: 'white',
                            }
                          }}
                        >
                          {subItem.label}
                        </MenuItem>
                      ))}
                    </Menu>
                  )}
                </Box>
              ))}
                <Button
                  variant="contained"
                  sx={{
                    ml: 2,
                    backgroundColor: '#ff9800',
                    color: 'white',
                    textTransform: 'none',
                    fontWeight: 600,
                    px: 3,
                    py: 1,
                    borderRadius: 2,
                    boxShadow: '0 2px 8px rgba(255, 152, 0, 0.3)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      backgroundColor: '#f57c00',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(255, 152, 0, 0.4)',
                    }
                  }}
                >
                  Apply Now
                </Button>
              </Box>
            )}

            {/* Mobile Menu Button */}
            {isMobile && (
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{
                  backgroundColor: 'rgba(25, 118, 210, 0.1)',
                  borderRadius: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: '#1976d2',
                    color: 'white',
                    transform: 'rotate(90deg)',
                  }
                }}
              >
                <MenuIcon />
              </IconButton>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: 300,
            background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
            color: 'white',
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Spacer for fixed header */}
      <Box sx={{ height: scrolled ? '70px' : '118px', transition: 'height 0.3s ease' }} />
    </>
  )
}

export default Header
