import * as React from 'react';
type MobileDateRangePickerComponent = (<TDate>(props: MobileDateRangePickerProps & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * @deprecated The MobileDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.
 * @ignore - do not document.
 */
declare const MobileDateRangePicker: MobileDateRangePickerComponent;
export default MobileDateRangePicker;
export type MobileDateRangePickerProps = Record<any, any>;