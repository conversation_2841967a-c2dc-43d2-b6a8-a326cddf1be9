import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Chip,
  Button,
  Fade,
  Breadcrumbs,
  Link,
  Divider,
} from '@mui/material'
import {
  Computer,
  ElectricalServices,
  DirectionsCar,
  Build,
  Engineering,
  Biotech,
  Agriculture,
  Restaurant,
  HealthAndSafety,
  BusinessCenter,
  School,
  Home,
  ArrowBack,
} from '@mui/icons-material'
import { useState } from 'react'

const AllProgramsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('All')

  const programs = [
    // Engineering & Technology
    {
      id: 1,
      title: 'Computer System & Architecture',
      category: 'Engineering & Technology',
      level: 'L3-L5',
      duration: '3 Years',
      description: 'Master computer systems, programming, and 3D printing technology. Learn hardware design, software development, and advanced manufacturing techniques.',
      image: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop',
      icon: <Computer />,
      color: '#2196f3',
      features: ['Computer Programming', 'System Architecture', '3D Printing', 'Hardware Design'],
      careerPaths: ['Software Developer', 'System Administrator', 'IT Support Specialist'],
    },
    {
      id: 2,
      title: 'Electronics & Telecommunication',
      category: 'Engineering & Technology',
      level: 'L3-L5',
      duration: '3 Years',
      description: 'Explore electronics, circuit design, and telecommunication systems. Hands-on experience with soldering, signal processing, and communication networks.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=250&fit=crop',
      icon: <ElectricalServices />,
      color: '#ff9800',
      features: ['Circuit Design', 'Soldering Techniques', 'Telecommunication', 'Signal Processing'],
      careerPaths: ['Electronics Technician', 'Telecom Engineer', 'Network Specialist'],
    },
    {
      id: 3,
      title: 'Automobile Technology',
      category: 'Engineering & Technology',
      level: 'L3-L5',
      duration: '3 Years',
      description: 'Learn automotive engineering, vehicle maintenance, and modern car manufacturing. Practical training in automotive systems and repair techniques.',
      image: 'https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?w=400&h=250&fit=crop',
      icon: <DirectionsCar />,
      color: '#4caf50',
      features: ['Engine Systems', 'Vehicle Maintenance', 'Automotive Electronics', 'Manufacturing'],
      careerPaths: ['Automotive Technician', 'Mechanic', 'Auto Parts Specialist'],
    },
    {
      id: 4,
      title: 'Mechanical Engineering',
      category: 'Engineering & Technology',
      level: 'L3-L5',
      duration: '3 Years',
      description: 'Comprehensive mechanical engineering program covering design, manufacturing, and maintenance of mechanical systems.',
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=400&h=250&fit=crop',
      icon: <Engineering />,
      color: '#9c27b0',
      features: ['Machine Design', 'Manufacturing Processes', 'CAD/CAM', 'Quality Control'],
      careerPaths: ['Mechanical Technician', 'Manufacturing Engineer', 'Quality Inspector'],
    },
    // Construction & Building
    {
      id: 5,
      title: 'Building & Construction',
      category: 'Construction & Building',
      level: 'L3-L5',
      duration: '3 Years',
      description: 'Master construction techniques, building design, and project management. Learn modern construction methods and safety protocols.',
      image: 'https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=400&h=250&fit=crop',
      icon: <Build />,
      color: '#795548',
      features: ['Construction Methods', 'Building Design', 'Project Management', 'Safety Protocols'],
      careerPaths: ['Construction Supervisor', 'Building Inspector', 'Project Manager'],
    },
    {
      id: 6,
      title: 'Plumbing & Water Systems',
      category: 'Construction & Building',
      level: 'L3-L4',
      duration: '2 Years',
      description: 'Specialized training in plumbing systems, water supply, and sanitation. Learn installation, maintenance, and repair techniques.',
      image: 'https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=400&h=250&fit=crop',
      icon: <Build />,
      color: '#607d8b',
      features: ['Pipe Installation', 'Water Systems', 'Sanitation', 'Maintenance'],
      careerPaths: ['Plumber', 'Water Systems Technician', 'Maintenance Specialist'],
    },
    // Health & Sciences
    {
      id: 7,
      title: 'Laboratory Technology',
      category: 'Health & Sciences',
      level: 'L3-L5',
      duration: '3 Years',
      description: 'Advanced laboratory techniques and scientific analysis. Training in medical, chemical, and biological laboratory procedures.',
      image: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=400&h=250&fit=crop',
      icon: <Biotech />,
      color: '#e91e63',
      features: ['Laboratory Analysis', 'Scientific Equipment', 'Quality Testing', 'Research Methods'],
      careerPaths: ['Lab Technician', 'Quality Analyst', 'Research Assistant'],
    },
    {
      id: 8,
      title: 'Health & Safety Management',
      category: 'Health & Sciences',
      level: 'L3-L4',
      duration: '2 Years',
      description: 'Workplace safety, health regulations, and risk management. Essential for maintaining safe working environments.',
      image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=250&fit=crop',
      icon: <HealthAndSafety />,
      color: '#f44336',
      features: ['Safety Protocols', 'Risk Assessment', 'Health Regulations', 'Emergency Response'],
      careerPaths: ['Safety Officer', 'Health Inspector', 'Risk Manager'],
    },
    // Agriculture & Food
    {
      id: 9,
      title: 'Agriculture & Farming Technology',
      category: 'Agriculture & Food',
      level: 'L3-L5',
      duration: '3 Years',
      description: 'Modern farming techniques, crop management, and agricultural technology. Sustainable farming practices and food production.',
      image: 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=250&fit=crop',
      icon: <Agriculture />,
      color: '#8bc34a',
      features: ['Crop Management', 'Soil Science', 'Agricultural Technology', 'Sustainable Farming'],
      careerPaths: ['Agricultural Technician', 'Farm Manager', 'Crop Specialist'],
    },
    {
      id: 10,
      title: 'Food Processing & Technology',
      category: 'Agriculture & Food',
      level: 'L3-L4',
      duration: '2 Years',
      description: 'Food processing techniques, quality control, and food safety. Learn modern food production and preservation methods.',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=250&fit=crop',
      icon: <Restaurant />,
      color: '#ff5722',
      features: ['Food Processing', 'Quality Control', 'Food Safety', 'Preservation Methods'],
      careerPaths: ['Food Technologist', 'Quality Controller', 'Production Supervisor'],
    },
    // Business & Management
    {
      id: 11,
      title: 'Business Administration',
      category: 'Business & Management',
      level: 'L3-L5',
      duration: '3 Years',
      description: 'Comprehensive business management, entrepreneurship, and administrative skills. Prepare for leadership roles in various industries.',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=250&fit=crop',
      icon: <BusinessCenter />,
      color: '#3f51b5',
      features: ['Business Management', 'Entrepreneurship', 'Financial Planning', 'Leadership'],
      careerPaths: ['Business Manager', 'Entrepreneur', 'Administrative Officer'],
    },
    {
      id: 12,
      title: 'Hospitality & Tourism',
      category: 'Business & Management',
      level: 'L3-L4',
      duration: '2 Years',
      description: 'Hotel management, tourism services, and customer relations. Training for the growing hospitality industry.',
      image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=250&fit=crop',
      icon: <BusinessCenter />,
      color: '#00bcd4',
      features: ['Hotel Management', 'Customer Service', 'Tourism Planning', 'Event Management'],
      careerPaths: ['Hotel Manager', 'Tour Guide', 'Event Coordinator'],
    },
  ]

  const categories = ['All', 'Engineering & Technology', 'Construction & Building', 'Health & Sciences', 'Agriculture & Food', 'Business & Management']

  const filteredPrograms = selectedCategory === 'All' 
    ? programs 
    : programs.filter(program => program.category === selectedCategory)

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f8fafc' }}>
      {/* Header Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
          color: 'white',
          py: { xs: 6, md: 8 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `
              radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
            `,
            zIndex: 1,
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.history.back()}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <School sx={{ mr: 0.5 }} fontSize="inherit" />
              All Programs
            </Typography>
          </Breadcrumbs>

          {/* Back Button */}
          <Button
            startIcon={<ArrowBack />}
            onClick={() => window.history.back()}
            sx={{
              color: 'white',
              border: '1px solid rgba(255,255,255,0.3)',
              mb: 4,
              '&:hover': {
                bgcolor: 'rgba(255,255,255,0.1)',
                borderColor: 'rgba(255,255,255,0.5)',
              },
            }}
          >
            Back to Home
          </Button>

          {/* Page Title */}
          <Typography
            variant="h2"
            sx={{
              fontWeight: 800,
              mb: 2,
              fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem' },
              background: 'linear-gradient(45deg, #ffffff, #e2e8f0)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            All TVET Programs
          </Typography>
          
          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.8)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
            }}
          >
            Explore our comprehensive range of technical and vocational education programs designed to prepare you for success in today's competitive job market
          </Typography>
        </Container>
      </Box>

      {/* Filter Section */}
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              mb: 3,
              color: '#1e293b',
              textAlign: 'center',
            }}
          >
            Filter by Category
          </Typography>

          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 2,
              justifyContent: 'center',
              mb: 4,
            }}
          >
            {categories.map((category) => (
              <Chip
                key={category}
                label={category}
                onClick={() => setSelectedCategory(category)}
                variant={selectedCategory === category ? 'filled' : 'outlined'}
                sx={{
                  px: 2,
                  py: 1,
                  fontSize: '0.9rem',
                  fontWeight: 500,
                  bgcolor: selectedCategory === category ? '#1e293b' : 'transparent',
                  color: selectedCategory === category ? 'white' : '#64748b',
                  borderColor: '#e2e8f0',
                  '&:hover': {
                    bgcolor: selectedCategory === category ? '#334155' : '#f1f5f9',
                  },
                }}
              />
            ))}
          </Box>

          <Divider sx={{ mb: 4 }} />

          <Typography
            variant="h6"
            sx={{
              color: '#64748b',
              textAlign: 'center',
              mb: 4,
            }}
          >
            {filteredPrograms.length} Program{filteredPrograms.length !== 1 ? 's' : ''} Available
          </Typography>
        </Box>

        {/* Programs Grid */}
        <Grid container spacing={4}>
          {filteredPrograms.map((program, index) => (
            <Grid item xs={12} sm={6} lg={4} key={program.id}>
              <Fade
                in
                timeout={600 + index * 100}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                      '& .program-image': {
                        transform: 'scale(1.05)',
                      },
                      '& .program-icon': {
                        transform: 'scale(1.1) rotate(5deg)',
                      },
                    },
                  }}
                >
                  {/* Program Image */}
                  <CardMedia
                    component="img"
                    height="200"
                    image={program.image}
                    alt={program.title}
                    className="program-image"
                    sx={{
                      transition: 'transform 0.3s ease',
                      objectFit: 'cover',
                    }}
                  />

                  {/* Program Icon */}
                  <Box
                    className="program-icon"
                    sx={{
                      position: 'absolute',
                      top: 15,
                      right: 15,
                      width: 50,
                      height: 50,
                      bgcolor: program.color,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
                      transition: 'all 0.3s ease',
                      fontSize: '1.5rem',
                    }}
                  >
                    {program.icon}
                  </Box>

                  {/* Level Badge */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 15,
                      left: 15,
                      bgcolor: 'rgba(255, 255, 255, 0.95)',
                      color: program.color,
                      px: 2,
                      py: 0.5,
                      borderRadius: 2,
                      fontSize: '0.8rem',
                      fontWeight: 700,
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    }}
                  >
                    {program.level}
                  </Box>

                  <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    {/* Category Chip */}
                    <Chip
                      label={program.category}
                      size="small"
                      sx={{
                        alignSelf: 'flex-start',
                        mb: 2,
                        bgcolor: `${program.color}15`,
                        color: program.color,
                        fontWeight: 600,
                        fontSize: '0.75rem',
                      }}
                    />

                    {/* Program Title */}
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        mb: 1.5,
                        color: '#1e293b',
                        fontSize: '1.2rem',
                        lineHeight: 1.3,
                      }}
                    >
                      {program.title}
                    </Typography>

                    {/* Program Description */}
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        fontSize: '0.9rem',
                        lineHeight: 1.6,
                        mb: 2,
                        flexGrow: 1,
                      }}
                    >
                      {program.description}
                    </Typography>

                    {/* Features */}
                    <Box sx={{ mb: 2 }}>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 600,
                          mb: 1,
                          color: '#374151',
                          fontSize: '0.85rem',
                        }}
                      >
                        Key Features:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {program.features.slice(0, 3).map((feature, idx) => (
                          <Chip
                            key={idx}
                            label={feature}
                            size="small"
                            variant="outlined"
                            sx={{
                              fontSize: '0.7rem',
                              height: 24,
                              borderColor: program.color,
                              color: program.color,
                            }}
                          />
                        ))}
                      </Box>
                    </Box>

                    {/* Career Paths */}
                    <Box sx={{ mb: 3 }}>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 600,
                          mb: 1,
                          color: '#374151',
                          fontSize: '0.85rem',
                        }}
                      >
                        Career Opportunities:
                      </Typography>
                      {program.careerPaths.slice(0, 2).map((career, idx) => (
                        <Typography
                          key={idx}
                          variant="body2"
                          sx={{
                            color: '#6b7280',
                            fontSize: '0.8rem',
                            '&::before': {
                              content: '"•"',
                              marginRight: 1,
                              color: program.color,
                              fontWeight: 'bold',
                            },
                          }}
                        >
                          {career}
                        </Typography>
                      ))}
                    </Box>

                    {/* Duration Info */}
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        pt: 2,
                        borderTop: '1px solid rgba(0,0,0,0.05)',
                        mt: 'auto',
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          color: program.color,
                          fontWeight: 700,
                          fontSize: '0.9rem',
                        }}
                      >
                        Duration: {program.duration}
                      </Typography>
                      <Button
                        size="small"
                        sx={{
                          color: program.color,
                          fontWeight: 600,
                          fontSize: '0.8rem',
                          '&:hover': {
                            bgcolor: `${program.color}10`,
                          },
                        }}
                      >
                        Learn More
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Fade>
            </Grid>
          ))}
        </Grid>

        {/* Call to Action Section */}
        <Box
          sx={{
            textAlign: 'center',
            mt: 8,
            py: 6,
            background: 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)',
            borderRadius: 4,
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '1.8rem', sm: '2.2rem' },
            }}
          >
            Ready to Start Your Journey?
          </Typography>
          <Typography
            variant="h6"
            sx={{
              color: '#64748b',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              fontSize: { xs: '1rem', sm: '1.1rem' },
            }}
          >
            Join thousands of students who have built successful careers through our comprehensive TVET programs
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                bgcolor: '#1e293b',
                color: 'white',
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
                borderRadius: 3,
                boxShadow: '0 4px 15px rgba(30, 41, 59, 0.2)',
                '&:hover': {
                  bgcolor: '#334155',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(30, 41, 59, 0.3)',
                },
              }}
            >
              Apply Now
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderColor: '#1e293b',
                color: '#1e293b',
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
                borderRadius: 3,
                '&:hover': {
                  bgcolor: '#1e293b',
                  color: 'white',
                  transform: 'translateY(-2px)',
                },
              }}
            >
              Contact Admissions
            </Button>
          </Box>
        </Box>
      </Container>
    </Box>
  )
}

export default AllProgramsPage
