import React, { useState } from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  Avatar,
  Chip,
  Button,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Rating,
  LinearProgress,
} from '@mui/material'
import {
  Home,
  School,
  Person,
  Star,
  EmojiEvents,
  Work,
  Email,
  Phone,
  LocationOn,
  Computer,
  Build,
  Engineering,
  Science,
  Psychology,
  Language,
} from '@mui/icons-material'

const AcademicStaffPage = () => {
  const [selectedDepartment, setSelectedDepartment] = useState(0)

  const departments = [
    'All Departments',
    'Electronics & Telecommunications',
    'Computer Science & IT',
    'Automotive Technology',
    'General Studies'
  ]

  const staffMembers = [
    {
      name: 'Dr. <PERSON>',
      position: 'Head of Electronics Department',
      department: 'Electronics & Telecommunications',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
      qualifications: ['PhD in Electronics Engineering', 'MSc in Telecommunications'],
      experience: '15 years',
      specialties: ['Digital Signal Processing', 'Wireless Communications', 'Circuit Design'],
      email: '<EMAIL>',
      phone: '+250 788 123 456',
      rating: 4.9,
      courses: ['Advanced Electronics', 'Telecommunications Systems', 'Digital Signal Processing'],
      achievements: [
        'Published 25+ research papers',
        'IEEE Senior Member',
        'Best Teacher Award 2023'
      ]
    },
    {
      name: 'Eng. Marie Claire Mukamana',
      position: 'Senior Lecturer - Computer Science',
      department: 'Computer Science & IT',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
      qualifications: ['MSc in Computer Science', 'BSc in Software Engineering'],
      experience: '12 years',
      specialties: ['Software Development', 'Database Systems', 'Web Technologies'],
      email: '<EMAIL>',
      phone: '+250 788 234 567',
      rating: 4.8,
      courses: ['Programming Fundamentals', 'Database Design', 'Web Development'],
      achievements: [
        'Google Certified Trainer',
        'Led 10+ industry projects',
        'Student Choice Award 2022'
      ]
    },
    {
      name: 'Mr. Patrick Nzeyimana',
      position: 'Automotive Technology Instructor',
      department: 'Automotive Technology',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
      qualifications: ['BSc in Mechanical Engineering', 'Automotive Technology Certificate'],
      experience: '10 years',
      specialties: ['Engine Diagnostics', 'Hybrid Vehicles', 'Automotive Electronics'],
      email: '<EMAIL>',
      phone: '+250 788 345 678',
      rating: 4.7,
      courses: ['Automotive Systems', 'Engine Technology', 'Vehicle Diagnostics'],
      achievements: [
        'ASE Master Technician',
        'Industry Partnership Coordinator',
        'Innovation Award 2023'
      ]
    },
    {
      name: 'Dr. Agnes Uwimana',
      position: 'Mathematics & Physics Lecturer',
      department: 'General Studies',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face',
      qualifications: ['PhD in Applied Mathematics', 'MSc in Physics'],
      experience: '18 years',
      specialties: ['Applied Mathematics', 'Engineering Physics', 'Statistics'],
      email: '<EMAIL>',
      phone: '+250 788 456 789',
      rating: 4.9,
      courses: ['Engineering Mathematics', 'Applied Physics', 'Statistics'],
      achievements: [
        'Excellence in Teaching Award',
        'Research Grant Recipient',
        'Mathematics Olympiad Coach'
      ]
    },
    {
      name: 'Mr. Samuel Habimana',
      position: 'Electronics Workshop Supervisor',
      department: 'Electronics & Telecommunications',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=300&fit=crop&crop=face',
      qualifications: ['BSc in Electronics Engineering', 'Technical Training Certificate'],
      experience: '8 years',
      specialties: ['PCB Design', 'Microcontrollers', 'Laboratory Management'],
      email: '<EMAIL>',
      phone: '+250 788 567 890',
      rating: 4.6,
      courses: ['Electronics Lab', 'Microcontroller Programming', 'Circuit Analysis'],
      achievements: [
        'Lab Safety Excellence Award',
        'Student Mentor of the Year',
        'Equipment Innovation Award'
      ]
    },
    {
      name: 'Ms. Claudine Uwimana',
      position: 'English & Communication Lecturer',
      department: 'General Studies',
      image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=300&h=300&fit=crop&crop=face',
      qualifications: ['MA in English Literature', 'TESOL Certificate'],
      experience: '9 years',
      specialties: ['Technical Writing', 'Business Communication', 'English Literature'],
      email: '<EMAIL>',
      phone: '+250 788 678 901',
      rating: 4.8,
      courses: ['Technical English', 'Business Communication', 'Report Writing'],
      achievements: [
        'Language Excellence Award',
        'International Conference Speaker',
        'Curriculum Development Lead'
      ]
    }
  ]

  const filteredStaff = selectedDepartment === 0 
    ? staffMembers 
    : staffMembers.filter(staff => staff.department === departments[selectedDepartment])

  const departmentStats = [
    { department: 'Electronics & Telecommunications', count: 8, icon: <Engineering /> },
    { department: 'Computer Science & IT', count: 6, icon: <Computer /> },
    { department: 'Automotive Technology', count: 5, icon: <Build /> },
    { department: 'General Studies', count: 4, icon: <School /> },
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #1565c0 0%, #1976d2 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Background */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 25% 75%, rgba(33, 150, 243, 0.3) 0%, transparent 50%), radial-gradient(circle at 75% 25%, rgba(63, 81, 181, 0.3) 0%, transparent 50%)',
            animation: 'float 18s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-18px) rotate(1deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <Person sx={{ mr: 0.5 }} fontSize="inherit" />
              Academic Staff
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Our Academic Staff
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Meet our dedicated team of experienced educators and industry professionals who are 
            committed to providing world-class technical education and mentorship.
          </Typography>
        </Container>
      </Box>

      {/* Department Statistics */}
      <Container maxWidth="lg" sx={{ py: { xs: 4, md: 6 } }}>
        <Grid container spacing={3}>
          {departmentStats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={1200 + index * 100}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: '#1976d2',
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        boxShadow: '0 8px 25px rgba(25, 118, 210, 0.4)',
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Typography variant="h4" fontWeight={700} color="#1976d2" mb={1}>
                      {stat.count}
                    </Typography>
                    <Typography variant="body2" color="#64748b" fontWeight={500}>
                      {stat.department}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Department Filter Tabs */}
      <Container maxWidth="lg" sx={{ mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
          <Tabs
            value={selectedDepartment}
            onChange={(e, newValue) => setSelectedDepartment(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem',
                minWidth: 'auto',
                px: 3,
                py: 2,
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(25, 118, 210, 0.1)',
                  transform: 'translateY(-2px)',
                },
              },
              '& .Mui-selected': {
                color: '#1976d2 !important',
                bgcolor: 'rgba(25, 118, 210, 0.1)',
              },
              '& .MuiTabs-indicator': {
                height: 3,
                borderRadius: 2,
                bgcolor: '#1976d2',
              },
            }}
          >
            {departments.map((dept, index) => (
              <Tab key={index} label={dept} />
            ))}
          </Tabs>
        </Box>
      </Container>

      {/* Staff Cards */}
      <Container maxWidth="lg" sx={{ pb: { xs: 6, md: 8 } }}>
        <Grid container spacing={4}>
          {filteredStaff.map((staff, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Grow in timeout={1600 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-12px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    {/* Profile Section */}
                    <Box sx={{ textAlign: 'center', mb: 3 }}>
                      <Avatar
                        src={staff.image}
                        sx={{
                          width: 100,
                          height: 100,
                          mx: 'auto',
                          mb: 2,
                          border: '4px solid #1976d2',
                          boxShadow: '0 8px 25px rgba(25, 118, 210, 0.3)',
                        }}
                      />
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                        }}
                      >
                        {staff.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#1976d2',
                          fontWeight: 500,
                          mb: 1,
                        }}
                      >
                        {staff.position}
                      </Typography>
                      <Chip
                        label={staff.department}
                        size="small"
                        sx={{
                          bgcolor: 'rgba(25, 118, 210, 0.1)',
                          color: '#1976d2',
                          fontWeight: 500,
                        }}
                      />
                    </Box>

                    {/* Rating */}
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                      <Rating value={staff.rating} precision={0.1} readOnly size="small" />
                      <Typography variant="body2" sx={{ ml: 1, color: '#64748b' }}>
                        ({staff.rating})
                      </Typography>
                    </Box>

                    {/* Experience */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" color="#64748b" gutterBottom>
                        <strong>Experience:</strong> {staff.experience}
                      </Typography>
                      <Typography variant="body2" color="#64748b">
                        <strong>Specialties:</strong> {staff.specialties.join(', ')}
                      </Typography>
                    </Box>

                    {/* Contact Info */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1, color: '#64748b' }}>
                        <Email sx={{ fontSize: '1rem', mr: 1, color: '#1976d2' }} />
                        {staff.email}
                      </Typography>
                      <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', color: '#64748b' }}>
                        <Phone sx={{ fontSize: '1rem', mr: 1, color: '#1976d2' }} />
                        {staff.phone}
                      </Typography>
                    </Box>

                    {/* View Profile Button */}
                    <Button
                      variant="contained"
                      fullWidth
                      sx={{
                        bgcolor: '#1976d2',
                        color: 'white',
                        fontWeight: 600,
                        py: 1.5,
                        borderRadius: 2,
                        textTransform: 'none',
                        '&:hover': {
                          bgcolor: '#1565c0',
                        },
                      }}
                    >
                      View Full Profile
                    </Button>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  )
}

export default AcademicStaffPage
