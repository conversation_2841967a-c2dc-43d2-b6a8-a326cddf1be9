import React from 'react'
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Fade,
  Grow,
} from '@mui/material'
import {
  EmojiEvents,
  School,
  TrendingUp,
  Groups,
  SportsSoccer,
  Engineering,
  Star,
  CheckCircle,
} from '@mui/icons-material'

const WhyJoinSection = () => {
  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const achievements = [
    {
      icon: <EmojiEvents />,
      title: 'National Excellence',
      description: '5th place in Industrial Electronics (2024 NESA TVET)',
      color: '#ffd700',
    },
    {
      icon: <TrendingUp />,
      title: 'Top Performance',
      description: '10th place in Transport & Logistics (2024 NESA TVET)',
      color: '#4caf50',
    },
    {
      icon: <SportsSoccer />,
      title: 'Sports Champions',
      description: 'Strong volleyball team - Division II champions',
      color: '#2196f3',
    },
    {
      icon: <Engineering />,
      title: 'TVET Excellence',
      description: 'Level 3-5 certifications in 12+ technical programs',
      color: '#ff9800',
    },
  ]

  const reasons = [
    {
      icon: <School />,
      title: 'Quality Education',
      description: 'Learner-centered education with established teaching skills and strong school culture',
    },
    {
      icon: <Groups />,
      title: 'Supportive Environment',
      description: 'High-performing, disciplined, ethical, and self-developmental learning environment',
    },
    {
      icon: <Star />,
      title: 'Proven Results',
      description: 'National recognition in technical fields with excellent student outcomes',
    },
    {
      icon: <CheckCircle />,
      title: 'Career Ready',
      description: 'Comprehensive skill development for tomorrow\'s technological challenges',
    },
  ]

  return (
    <Box
      sx={{
        py: { xs: 6, md: 10 },
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%)
          `,
          zIndex: 1,
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Section Header */}
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 3,
                color: '#1e293b',
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -12,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 4,
                  background: 'linear-gradient(90deg, #2196f3, #ff9800, #4caf50)',
                  borderRadius: 2,
                },
              }}
            >
              Why Choose Nyanza TSS?
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#64748b',
                maxWidth: '700px',
                mx: 'auto',
                fontSize: { xs: '1rem', sm: '1.2rem' },
                lineHeight: 1.6,
                mt: 4,
              }}
            >
              Excellence in TVET education with proven national recognition and comprehensive technical training
            </Typography>
          </Box>
        </Fade>

        {/* Achievement Highlights */}
        <Box sx={{ mb: { xs: 6, md: 8 } }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 4,
              textAlign: 'center',
              color: '#1e293b',
              fontSize: { xs: '1.5rem', sm: '2rem' },
            }}
          >
            🏆 Our Achievements
          </Typography>
          <Grid container spacing={3}>
            {achievements.map((achievement, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Grow in timeout={800 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.9)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 3, textAlign: 'center' }}>
                      <Box
                        sx={{
                          width: 60,
                          height: 60,
                          bgcolor: achievement.color,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          mx: 'auto',
                          mb: 2,
                          fontSize: '1.8rem',
                        }}
                      >
                        {achievement.icon}
                      </Box>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 700,
                          mb: 1,
                          color: '#1e293b',
                          fontSize: '1.1rem',
                        }}
                      >
                        {achievement.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          fontSize: '0.9rem',
                          lineHeight: 1.5,
                        }}
                      >
                        {achievement.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Why Choose Us Reasons */}
        <Box sx={{ mb: { xs: 6, md: 8 } }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 4,
              textAlign: 'center',
              color: '#1e293b',
              fontSize: { xs: '1.5rem', sm: '2rem' },
            }}
          >
            Why Students Choose Us
          </Typography>
          <Grid container spacing={4}>
            {reasons.map((reason, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Fade in timeout={1000 + index * 200}>
                  <Box
                    sx={{
                      textAlign: 'center',
                      p: 3,
                      height: '100%',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                      },
                    }}
                  >
                    <Box
                      sx={{
                        width: 70,
                        height: 70,
                        bgcolor: '#1e293b',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 3,
                        fontSize: '2rem',
                        boxShadow: '0 4px 20px rgba(30, 41, 59, 0.2)',
                      }}
                    >
                      {reason.icon}
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        mb: 2,
                        color: '#1e293b',
                        fontSize: '1.2rem',
                      }}
                    >
                      {reason.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        fontSize: '0.95rem',
                        lineHeight: 1.6,
                      }}
                    >
                      {reason.description}
                    </Typography>
                  </Box>
                </Fade>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Call to Action */}
        <Fade in timeout={1500}>
          <Box
            sx={{
              textAlign: 'center',
              py: 6,
              background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
              borderRadius: 4,
              color: 'white',
              position: 'relative',
              overflow: 'hidden',
            }}
          >
            {/* Background Pattern */}
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundImage: `
                  radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
                `,
                zIndex: 1,
              }}
            />

            <Box sx={{ position: 'relative', zIndex: 2 }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  mb: 2,
                  fontSize: { xs: '1.8rem', sm: '2.2rem' },
                }}
              >
                Ready to Join Excellence?
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: 'rgba(255,255,255,0.8)',
                  mb: 4,
                  maxWidth: '600px',
                  mx: 'auto',
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                }}
              >
                Join Nyanza Technical Secondary School and become part of our legacy of excellence in TVET education
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => scrollToSection('#programs')}
                  sx={{
                    bgcolor: 'white',
                    color: '#1e293b',
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    borderRadius: 3,
                    '&:hover': {
                      bgcolor: '#f1f5f9',
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  Explore Programs
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={() => scrollToSection('#contacts')}
                  sx={{
                    borderColor: 'white',
                    color: 'white',
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    borderRadius: 3,
                    '&:hover': {
                      bgcolor: 'rgba(255,255,255,0.1)',
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  Contact Us
                </Button>
              </Box>
            </Box>
          </Box>
        </Fade>
      </Container>
    </Box>
  )
}

export default WhyJoinSection
