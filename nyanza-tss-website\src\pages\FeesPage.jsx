import React from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material'
import {
  Home,
  Payment,
  School,
  CheckCircle,
  ExpandMore,
  AccountBalance,
  CreditCard,
  MonetizationOn,
  Savings,
  TrendingDown,
  Star,
  Info,
} from '@mui/icons-material'

const FeesPage = () => {
  const feeStructure = [
    {
      level: 'Level 3 (Certificate)',
      duration: '1 Year',
      programs: ['Basic Electronics', 'Computer Basics', 'Automotive Basics'],
      fees: {
        tuition: 180000,
        registration: 15000,
        materials: 25000,
        total: 220000
      },
      color: '#1976d2'
    },
    {
      level: 'Level 4 (Diploma)',
      duration: '2 Years',
      programs: ['Electronics & Telecommunications', 'Computer Science', 'Automotive Technology'],
      fees: {
        tuition: 250000,
        registration: 20000,
        materials: 35000,
        total: 305000
      },
      color: '#2e7d32'
    },
    {
      level: 'Level 5 (Advanced Diploma)',
      duration: '3 Years',
      programs: ['Advanced Electronics', 'Software Development', 'Automotive Engineering'],
      fees: {
        tuition: 320000,
        registration: 25000,
        materials: 45000,
        total: 390000
      },
      color: '#ed6c02'
    },
  ]

  const paymentMethods = [
    {
      method: 'Bank Transfer',
      description: 'Direct transfer to school account',
      icon: <AccountBalance />,
      details: [
        'Bank of Kigali - Account: ***********',
        'Equity Bank - Account: ***********',
        'Include student name in reference',
        'Processing time: 1-2 business days'
      ],
      color: '#1976d2'
    },
    {
      method: 'Mobile Money',
      description: 'MTN MoMo or Airtel Money',
      icon: <CreditCard />,
      details: [
        'MTN MoMo: *182*8*1*123456#',
        'Airtel Money: *500*9*123456#',
        'Agent code: NTSS2024',
        'Instant confirmation'
      ],
      color: '#2e7d32'
    },
    {
      method: 'Installment Plan',
      description: 'Pay in manageable installments',
      icon: <Savings />,
      details: [
        '50% at enrollment',
        '25% mid-semester',
        '25% before final exams',
        'No additional charges'
      ],
      color: '#ed6c02'
    },
  ]

  const scholarships = [
    {
      name: 'Academic Excellence Scholarship',
      coverage: '50% Tuition Fee Reduction',
      criteria: 'Top 10% academic performance',
      icon: <Star />,
      color: '#ffd700'
    },
    {
      name: 'Need-Based Financial Aid',
      coverage: '30% Total Fee Reduction',
      criteria: 'Demonstrated financial need',
      icon: <TrendingDown />,
      color: '#2e7d32'
    },
    {
      name: 'Technical Innovation Award',
      coverage: 'Full Tuition Coverage',
      criteria: 'Outstanding technical project',
      icon: <School />,
      color: '#1976d2'
    },
  ]

  const additionalCosts = [
    { item: 'Uniform (Complete Set)', cost: 35000, required: true },
    { item: 'Safety Equipment', cost: 25000, required: true },
    { item: 'Textbooks & Materials', cost: 40000, required: true },
    { item: 'Laboratory Access Card', cost: 10000, required: true },
    { item: 'Student ID Card', cost: 5000, required: true },
    { item: 'Accommodation (Per Semester)', cost: 120000, required: false },
    { item: 'Meal Plan (Per Semester)', cost: 180000, required: false },
    { item: 'Transport (Monthly)', cost: 25000, required: false },
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #2e7d32 0%, #388e3c 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Background */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 30% 70%, rgba(76, 175, 80, 0.3) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(139, 195, 74, 0.3) 0%, transparent 50%)',
            animation: 'float 15s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-15px) rotate(1deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <Payment sx={{ mr: 0.5 }} fontSize="inherit" />
              School Fees
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8fff8 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              School Fees Structure
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Transparent and affordable fee structure designed to make quality technical education 
            accessible to all students. Explore our payment options and scholarship opportunities.
          </Typography>
        </Container>
      </Box>

      {/* Fee Structure Cards */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={1200}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Program Fee Structure
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Annual fees for our TVET programs (All amounts in Rwandan Francs)
        </Typography>

        <Grid container spacing={4}>
          {feeStructure.map((program, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Grow in timeout={1400 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-12px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 4,
                      background: `linear-gradient(90deg, ${program.color} 0%, ${program.color}80 100%)`,
                      animation: 'shimmer 2s ease-in-out infinite',
                      '@keyframes shimmer': {
                        '0%, 100%': { opacity: 0.8 },
                        '50%': { opacity: 1 },
                      },
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box sx={{ textAlign: 'center', mb: 3 }}>
                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 700,
                          color: program.color,
                          mb: 1,
                        }}
                      >
                        {program.level}
                      </Typography>
                      <Chip
                        label={program.duration}
                        sx={{
                          bgcolor: `${program.color}20`,
                          color: program.color,
                          fontWeight: 600,
                        }}
                      />
                    </Box>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        mb: 3,
                        textAlign: 'center',
                        fontStyle: 'italic',
                      }}
                    >
                      Programs: {program.programs.join(', ')}
                    </Typography>

                    <Box sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="#64748b">Tuition Fee:</Typography>
                        <Typography variant="body2" fontWeight={600}>
                          {program.fees.tuition.toLocaleString()} RWF
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="#64748b">Registration:</Typography>
                        <Typography variant="body2" fontWeight={600}>
                          {program.fees.registration.toLocaleString()} RWF
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="body2" color="#64748b">Materials:</Typography>
                        <Typography variant="body2" fontWeight={600}>
                          {program.fees.materials.toLocaleString()} RWF
                        </Typography>
                      </Box>
                      <Divider sx={{ mb: 2 }} />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="h6" fontWeight={700} color={program.color}>
                          Total Annual:
                        </Typography>
                        <Typography variant="h6" fontWeight={700} color={program.color}>
                          {program.fees.total.toLocaleString()} RWF
                        </Typography>
                      </Box>
                    </Box>

                    <Button
                      variant="contained"
                      fullWidth
                      sx={{
                        bgcolor: program.color,
                        color: 'white',
                        fontWeight: 600,
                        py: 1.5,
                        borderRadius: 2,
                        textTransform: 'none',
                        '&:hover': {
                          bgcolor: program.color,
                          filter: 'brightness(0.9)',
                        },
                      }}
                    >
                      Apply for {program.level}
                    </Button>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Payment Methods */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={1800}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Payment Methods
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Choose from multiple convenient payment options that suit your needs
          </Typography>

          <Grid container spacing={4}>
            {paymentMethods.map((method, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Grow in timeout={2000 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4, textAlign: 'center' }}>
                      <Box
                        sx={{
                          width: 70,
                          height: 70,
                          bgcolor: method.color,
                          borderRadius: 3,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          mx: 'auto',
                          mb: 3,
                          boxShadow: `0 8px 25px ${method.color}40`,
                        }}
                      >
                        {method.icon}
                      </Box>

                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          mb: 2,
                          color: '#1e293b',
                        }}
                      >
                        {method.method}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          mb: 3,
                          lineHeight: 1.6,
                        }}
                      >
                        {method.description}
                      </Typography>

                      <List>
                        {method.details.map((detail, detailIndex) => (
                          <ListItem key={detailIndex} sx={{ px: 0, py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 30 }}>
                              <CheckCircle sx={{ fontSize: '1rem', color: method.color }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={detail}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.85rem',
                                  color: '#64748b',
                                },
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Scholarships Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2200}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Scholarships & Financial Aid
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          We believe in making quality education accessible through various scholarship programs
        </Typography>

        <Grid container spacing={4}>
          {scholarships.map((scholarship, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Grow in timeout={2400 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: `linear-gradient(135deg, ${scholarship.color}10 0%, ${scholarship.color}05 100%)`,
                    borderRadius: 3,
                    border: `2px solid ${scholarship.color}30`,
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: `0 20px 60px ${scholarship.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 4, textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 70,
                        height: 70,
                        bgcolor: scholarship.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 3,
                        boxShadow: `0 8px 25px ${scholarship.color}40`,
                      }}
                    >
                      {scholarship.icon}
                    </Box>

                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        color: '#1e293b',
                      }}
                    >
                      {scholarship.name}
                    </Typography>

                    <Chip
                      label={scholarship.coverage}
                      sx={{
                        bgcolor: scholarship.color,
                        color: 'white',
                        fontWeight: 600,
                        mb: 2,
                        fontSize: '0.9rem',
                      }}
                    />

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                        mt: 2,
                      }}
                    >
                      <strong>Eligibility:</strong> {scholarship.criteria}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Additional Costs */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2600}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Additional Costs
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Plan your budget with our comprehensive breakdown of additional expenses
          </Typography>

          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Card
                sx={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 3,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 600,
                      mb: 3,
                      color: '#1e293b',
                      textAlign: 'center',
                    }}
                  >
                    Required Expenses
                  </Typography>

                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600, color: '#1e293b' }}>Item</TableCell>
                          <TableCell align="right" sx={{ fontWeight: 600, color: '#1e293b' }}>Cost (RWF)</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {additionalCosts.filter(cost => cost.required).map((cost, index) => (
                          <TableRow key={index}>
                            <TableCell sx={{ color: '#64748b' }}>{cost.item}</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 600, color: '#1e293b' }}>
                              {cost.cost.toLocaleString()}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card
                sx={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 3,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 600,
                      mb: 3,
                      color: '#1e293b',
                      textAlign: 'center',
                    }}
                  >
                    Optional Expenses
                  </Typography>

                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600, color: '#1e293b' }}>Item</TableCell>
                          <TableCell align="right" sx={{ fontWeight: 600, color: '#1e293b' }}>Cost (RWF)</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {additionalCosts.filter(cost => !cost.required).map((cost, index) => (
                          <TableRow key={index}>
                            <TableCell sx={{ color: '#64748b' }}>{cost.item}</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 600, color: '#1e293b' }}>
                              {cost.cost.toLocaleString()}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #2e7d32 0%, #388e3c 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={2800}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Need Financial Assistance?
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Our financial aid office is here to help you explore payment options and scholarship opportunities.
            Don't let finances be a barrier to your technical education.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                bgcolor: 'white',
                color: '#2e7d32',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Apply for Scholarship
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              Contact Financial Aid
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default FeesPage
