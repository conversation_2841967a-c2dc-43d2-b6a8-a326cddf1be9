import React from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  Button,
  Chip,
} from '@mui/material'
import {
  Home,
  Work,
  School,
  Engineering,
  Computer,
  Science,
  Build,
  Groups,
  LocationOn,
  Email,
  Phone,
  AccessTime,
} from '@mui/icons-material'

const CareersPage = () => {
  const jobOpenings = [
    {
      title: "Electronics & Telecommunications Instructor",
      department: "Technical Education",
      type: "Full-time",
      location: "Nyanza TSS Campus",
      description: "Seeking an experienced instructor to teach electronics and telecommunications courses for TVET Levels 3-5.",
      requirements: [
        "Bachelor's degree in Electronics/Telecommunications",
        "Minimum 3 years teaching experience",
        "TVET certification preferred",
        "Fluency in English and Kinyarwanda"
      ],
      icon: <Engineering />,
      color: "#ff9800",
      posted: "2 weeks ago"
    },
    {
      title: "Computer Science Teacher",
      department: "ICT Department",
      type: "Full-time",
      location: "Nyanza TSS Campus",
      description: "Join our ICT team to deliver quality computer science education and prepare students for the digital economy.",
      requirements: [
        "Bachelor's degree in Computer Science/IT",
        "Programming experience (Python, Java, C++)",
        "Teaching certification",
        "Experience with educational technology"
      ],
      icon: <Computer />,
      color: "#2196f3",
      posted: "1 week ago"
    },
    {
      title: "Automotive Technology Instructor",
      department: "Technical Education",
      type: "Full-time",
      location: "Nyanza TSS Campus",
      description: "Teach automotive technology and mechanics to students in our comprehensive automotive program.",
      requirements: [
        "Diploma/Degree in Automotive Technology",
        "Industry experience in automotive repair",
        "Teaching experience preferred",
        "ASE certification is a plus"
      ],
      icon: <Build />,
      color: "#4caf50",
      posted: "3 days ago"
    },
    {
      title: "Laboratory Technician",
      department: "Science Department",
      type: "Full-time",
      location: "Nyanza TSS Campus",
      description: "Support science and technical education by maintaining laboratory equipment and assisting with practical sessions.",
      requirements: [
        "Diploma in Laboratory Technology",
        "Experience with lab equipment maintenance",
        "Safety protocols knowledge",
        "Attention to detail"
      ],
      icon: <Science />,
      color: "#9c27b0",
      posted: "5 days ago"
    },
    {
      title: "Student Affairs Coordinator",
      department: "Administration",
      type: "Full-time",
      location: "Nyanza TSS Campus",
      description: "Coordinate student activities, welfare programs, and support services to enhance student life experience.",
      requirements: [
        "Bachelor's degree in Education/Social Work",
        "Experience in student affairs",
        "Strong communication skills",
        "Leadership and organizational abilities"
      ],
      icon: <Groups />,
      color: "#ff6b35",
      posted: "1 week ago"
    },
    {
      title: "Librarian",
      department: "Academic Support",
      type: "Part-time",
      location: "Nyanza TSS Campus",
      description: "Manage library resources, assist students with research, and maintain digital learning materials.",
      requirements: [
        "Diploma in Library Science",
        "Computer literacy",
        "Customer service skills",
        "Experience with digital resources"
      ],
      icon: <School />,
      color: "#607d8b",
      posted: "2 weeks ago"
    },
  ]

  const benefits = [
    {
      title: "Competitive Salary",
      description: "Attractive compensation package based on qualifications and experience",
      icon: "💰",
    },
    {
      title: "Professional Development",
      description: "Opportunities for training, workshops, and career advancement",
      icon: "📚",
    },
    {
      title: "Health Insurance",
      description: "Comprehensive health coverage for employees and their families",
      icon: "🏥",
    },
    {
      title: "Collaborative Environment",
      description: "Work with dedicated professionals committed to educational excellence",
      icon: "🤝",
    },
    {
      title: "Modern Facilities",
      description: "Access to state-of-the-art equipment and learning resources",
      icon: "🏢",
    },
    {
      title: "Impact & Purpose",
      description: "Make a meaningful difference in students' lives and Rwanda's development",
      icon: "🌟",
    },
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.3) 0%, transparent 50%)',
            animation: 'float 20s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px)' },
              '50%': { transform: 'translateY(-20px)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <Work sx={{ mr: 0.5 }} fontSize="inherit" />
              Work With Us
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
              }}
            >
              Join Our Team
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
            }}
          >
            Be part of Rwanda's leading technical education institution. Help shape the future by 
            empowering students with the skills they need to succeed in today's economy.
          </Typography>
        </Container>
      </Box>

      {/* Why Work With Us */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={1200}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Why Work With Us?
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Join a community of educators and professionals dedicated to excellence in technical education 
          and student success.
        </Typography>

        <Grid container spacing={4}>
          {benefits.map((benefit, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Grow in timeout={1400 + index * 100}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    <Typography
                      sx={{
                        fontSize: '3rem',
                        mb: 2,
                      }}
                    >
                      {benefit.icon}
                    </Typography>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        color: '#1e293b',
                      }}
                    >
                      {benefit.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                      }}
                    >
                      {benefit.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Current Openings */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={1600}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 6,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Current Job Openings
            </Typography>
          </Fade>

          <Grid container spacing={4}>
            {jobOpenings.map((job, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Grow in timeout={1800 + index * 100}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      {/* Header */}
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
                        <Box
                          sx={{
                            width: 50,
                            height: 50,
                            bgcolor: job.color,
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            mr: 2,
                            flexShrink: 0,
                          }}
                        >
                          {job.icon}
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 600,
                              color: '#1e293b',
                              mb: 1,
                            }}
                          >
                            {job.title}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
                            <Chip
                              label={job.department}
                              size="small"
                              sx={{ bgcolor: `${job.color}15`, color: job.color }}
                            />
                            <Chip
                              label={job.type}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: '#64748b' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <LocationOn sx={{ fontSize: '1rem' }} />
                              <Typography variant="body2">{job.location}</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <AccessTime sx={{ fontSize: '1rem' }} />
                              <Typography variant="body2">{job.posted}</Typography>
                            </Box>
                          </Box>
                        </Box>
                      </Box>

                      {/* Description */}
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          lineHeight: 1.6,
                          mb: 3,
                        }}
                      >
                        {job.description}
                      </Typography>

                      {/* Requirements */}
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                        }}
                      >
                        Requirements:
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mb: 3 }}>
                        {job.requirements.map((req, reqIndex) => (
                          <Typography
                            component="li"
                            variant="body2"
                            key={reqIndex}
                            sx={{
                              color: '#64748b',
                              mb: 0.5,
                            }}
                          >
                            {req}
                          </Typography>
                        ))}
                      </Box>

                      {/* Apply Button */}
                      <Button
                        variant="contained"
                        fullWidth
                        sx={{
                          bgcolor: job.color,
                          '&:hover': {
                            bgcolor: job.color,
                            filter: 'brightness(0.9)',
                          },
                        }}
                      >
                        Apply Now
                      </Button>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Contact Information */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Box sx={{ textAlign: 'center' }}>
          <Fade in timeout={2000}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Ready to Join Us?
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              color: '#64748b',
              fontSize: '1.1rem',
              lineHeight: 1.8,
              maxWidth: '600px',
              mx: 'auto',
              mb: 4,
            }}
          >
            Don't see a position that matches your skills? We're always looking for talented individuals 
            who share our passion for education. Send us your CV and let us know how you'd like to contribute.
          </Typography>

          <Grid container spacing={4} justifyContent="center" sx={{ mt: 4 }}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                <Phone sx={{ color: '#1976d2' }} />
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  0788309436
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                <Email sx={{ color: '#1976d2' }} />
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  <EMAIL>
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                <LocationOn sx={{ color: '#1976d2' }} />
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  Nyanza District, Rwanda
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Container>
    </Box>
  )
}

export default CareersPage
