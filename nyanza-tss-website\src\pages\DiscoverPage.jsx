import React from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Button,
  Avatar,
  LinearProgress,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Divider,
} from '@mui/material'
import {
  Home,
  Explore,
  School,
  Group,
  EmojiEvents,
  Restaurant,
  Hotel,
  DirectionsBus,
  LocalLibrary,
  Wifi,
  Security,
  HealthAndSafety,
  CheckCircle,
  Star,
  TrendingUp,
  Favorite,
  Psychology,
  SportsVolleyball,
  Computer,
  Build,
  Science,
} from '@mui/icons-material'

const DiscoverPage = () => {
  const campusLife = [
    {
      title: 'Academic Excellence',
      description: 'World-class technical education with hands-on learning',
      icon: <School />,
      color: '#1976d2',
      highlights: [
        'State-of-the-art laboratories',
        'Industry-experienced instructors',
        'Project-based learning',
        'Industry certifications'
      ]
    },
    {
      title: 'Vibrant Community',
      description: 'Diverse student body from across Rwanda and beyond',
      icon: <Group />,
      color: '#2e7d32',
      highlights: [
        '500+ active students',
        '15+ student clubs',
        'Cultural diversity',
        'Peer mentorship programs'
      ]
    },
    {
      title: 'Sports & Recreation',
      description: 'Championship volleyball team and recreational activities',
      icon: <SportsVolleyball />,
      color: '#ed6c02',
      highlights: [
        'National volleyball champions',
        'Modern sports facilities',
        'Intramural competitions',
        'Fitness programs'
      ]
    },
    {
      title: 'Innovation Hub',
      description: 'Fostering creativity and entrepreneurship',
      icon: <Psychology />,
      color: '#9c27b0',
      highlights: [
        'Student innovation projects',
        'Startup incubation',
        'Technology competitions',
        'Research opportunities'
      ]
    }
  ]

  const facilities = [
    {
      name: 'Modern Dormitories',
      description: 'Comfortable accommodation with study spaces',
      icon: <Hotel />,
      features: ['Wi-Fi enabled', 'Study rooms', 'Common areas', '24/7 security'],
      image: 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=400&h=300&fit=crop',
      color: '#1976d2'
    },
    {
      name: 'Dining Hall',
      description: 'Nutritious meals with local and international cuisine',
      icon: <Restaurant />,
      features: ['Balanced nutrition', 'Dietary accommodations', 'Fresh ingredients', 'Social dining'],
      image: 'https://images.unsplash.com/photo-1567521464027-f127ff144326?w=400&h=300&fit=crop',
      color: '#2e7d32'
    },
    {
      name: 'Library & Study Center',
      description: 'Extensive collection of technical resources',
      icon: <LocalLibrary />,
      features: ['Digital resources', 'Quiet study zones', 'Group study rooms', 'Research support'],
      image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop',
      color: '#ed6c02'
    },
    {
      name: 'Health Center',
      description: 'On-campus medical care and wellness programs',
      icon: <HealthAndSafety />,
      features: ['Medical staff', 'Emergency care', 'Health education', 'Mental health support'],
      image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop',
      color: '#d32f2f'
    },
    {
      name: 'Transportation',
      description: 'Convenient transport links to Nyanza town',
      icon: <DirectionsBus />,
      features: ['Regular shuttle service', 'Safe routes', 'Affordable fares', 'Weekend services'],
      image: 'https://images.unsplash.com/photo-**********-c4fd4a3d5957?w=400&h=300&fit=crop',
      color: '#ff9800'
    },
    {
      name: 'IT Infrastructure',
      description: 'High-speed internet and modern technology',
      icon: <Wifi />,
      features: ['Campus-wide Wi-Fi', 'Computer labs', 'Online learning platforms', 'Tech support'],
      image: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop',
      color: '#9c27b0'
    }
  ]

  const studentExperience = [
    {
      phase: 'Orientation Week',
      description: 'Welcome to Nyanza TSS family',
      activities: [
        'Campus tour and facility introduction',
        'Academic program overview',
        'Student services registration',
        'Social activities and team building'
      ]
    },
    {
      phase: 'Academic Integration',
      description: 'Settling into academic life',
      activities: [
        'Course registration and scheduling',
        'Laboratory safety training',
        'Study group formation',
        'Academic advisor meetings'
      ]
    },
    {
      phase: 'Skill Development',
      description: 'Building technical expertise',
      activities: [
        'Hands-on workshop training',
        'Industry project participation',
        'Certification preparation',
        'Internship opportunities'
      ]
    },
    {
      phase: 'Leadership & Growth',
      description: 'Developing leadership skills',
      activities: [
        'Student government participation',
        'Club leadership roles',
        'Community service projects',
        'Peer mentoring programs'
      ]
    },
    {
      phase: 'Career Preparation',
      description: 'Preparing for professional life',
      activities: [
        'Career counseling sessions',
        'Industry networking events',
        'Job placement assistance',
        'Alumni mentorship'
      ]
    }
  ]

  const achievements = [
    {
      title: 'Student Satisfaction',
      value: '96%',
      description: 'Students rate their experience as excellent',
      icon: <Favorite />,
      color: '#e91e63'
    },
    {
      title: 'Graduate Employment',
      value: '92%',
      description: 'Graduates employed within 6 months',
      icon: <TrendingUp />,
      color: '#4caf50'
    },
    {
      title: 'Industry Partnerships',
      value: '25+',
      description: 'Active partnerships with companies',
      icon: <Group />,
      color: '#2196f3'
    },
    {
      title: 'National Recognition',
      value: '5th',
      description: 'Ranked nationally in technical education',
      icon: <EmojiEvents />,
      color: '#ff9800'
    }
  ]

  const testimonials = [
    {
      name: 'Jean Claude Uwimana',
      program: 'Electronics & Telecommunications',
      year: 'Level 5',
      quote: 'Nyanza TSS has given me the practical skills and confidence to excel in the tech industry. The hands-on approach and supportive community make all the difference.',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      rating: 5
    },
    {
      name: 'Marie Grace Mukamana',
      program: 'Computer Science',
      year: 'Level 4',
      quote: 'The innovation hub at Nyanza TSS allowed me to develop my startup idea. The mentorship and resources available here are incredible.',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      rating: 5
    },
    {
      name: 'Patrick Nzeyimana',
      program: 'Automotive Technology',
      year: 'Graduate 2023',
      quote: 'The practical training I received at Nyanza TSS prepared me perfectly for my career. I now work as a senior technician at a leading automotive company.',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      rating: 5
    }
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Background */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 25% 75%, rgba(106, 17, 203, 0.3) 0%, transparent 50%), radial-gradient(circle at 75% 25%, rgba(37, 117, 252, 0.3) 0%, transparent 50%)',
            animation: 'float 22s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-22px) rotate(2deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <Explore sx={{ mr: 0.5 }} fontSize="inherit" />
              Discover Nyanza TSS
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Discover Nyanza TSS
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Experience life at Rwanda's premier technical secondary school. Discover our vibrant campus, 
            exceptional facilities, and the community that makes Nyanza TSS special.
          </Typography>
        </Container>
      </Box>

      {/* Achievement Stats */}
      <Container maxWidth="lg" sx={{ py: { xs: 4, md: 6 } }}>
        <Grid container spacing={3}>
          {achievements.map((achievement, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={1200 + index * 100}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${achievement.color}10 0%, ${achievement.color}05 100%)`,
                    borderRadius: 3,
                    border: `2px solid ${achievement.color}30`,
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: `0 20px 60px ${achievement.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: achievement.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        boxShadow: `0 8px 25px ${achievement.color}40`,
                      }}
                    >
                      {achievement.icon}
                    </Box>
                    <Typography variant="h3" fontWeight={700} color={achievement.color} mb={1}>
                      {achievement.value}
                    </Typography>
                    <Typography variant="h6" fontWeight={600} color="#1e293b" mb={1}>
                      {achievement.title}
                    </Typography>
                    <Typography variant="body2" color="#64748b" lineHeight={1.5}>
                      {achievement.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Campus Life */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={1600}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Campus Life at Nyanza TSS
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Experience a dynamic learning environment that combines academic excellence with personal growth
        </Typography>

        <Grid container spacing={4}>
          {campusLife.map((aspect, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Grow in timeout={1800 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Box
                        sx={{
                          width: 60,
                          height: 60,
                          bgcolor: aspect.color,
                          borderRadius: 3,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          mr: 3,
                          boxShadow: `0 8px 25px ${aspect.color}40`,
                        }}
                      >
                        {aspect.icon}
                      </Box>
                      <Box>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 600,
                            color: '#1e293b',
                            mb: 1,
                          }}
                        >
                          {aspect.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: '#64748b',
                          }}
                        >
                          {aspect.description}
                        </Typography>
                      </Box>
                    </Box>

                    <List dense>
                      {aspect.highlights.map((highlight, highlightIndex) => (
                        <ListItem key={highlightIndex} sx={{ px: 0, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 30 }}>
                            <CheckCircle sx={{ fontSize: '1rem', color: aspect.color }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={highlight}
                            sx={{
                              '& .MuiListItemText-primary': {
                                fontSize: '0.9rem',
                                color: '#64748b',
                              },
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Facilities */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2000}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              World-Class Facilities
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Modern facilities designed to support your academic journey and personal development
          </Typography>

          <Grid container spacing={4}>
            {facilities.map((facility, index) => (
              <Grid item xs={12} md={6} lg={4} key={index}>
                <Grow in timeout={2200 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      overflow: 'hidden',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <Box
                      sx={{
                        height: 200,
                        backgroundImage: `url(${facility.image})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        position: 'relative',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: `linear-gradient(45deg, ${facility.color}80 0%, ${facility.color}60 100%)`,
                        },
                      }}
                    >
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 16,
                          left: 16,
                          width: 50,
                          height: 50,
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          borderRadius: 2,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: facility.color,
                        }}
                      >
                        {facility.icon}
                      </Box>
                    </Box>

                    <CardContent sx={{ p: 4 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          mb: 2,
                          color: '#1e293b',
                        }}
                      >
                        {facility.name}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          mb: 3,
                          lineHeight: 1.6,
                        }}
                      >
                        {facility.description}
                      </Typography>

                      <List dense>
                        {facility.features.map((feature, featureIndex) => (
                          <ListItem key={featureIndex} sx={{ px: 0, py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 30 }}>
                              <CheckCircle sx={{ fontSize: '1rem', color: facility.color }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={feature}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.85rem',
                                  color: '#64748b',
                                },
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Student Experience Journey */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2400}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Your Journey at Nyanza TSS
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          From orientation to graduation, discover the transformative journey that awaits you
        </Typography>

        <Stepper orientation="vertical" sx={{ maxWidth: 800, mx: 'auto' }}>
          {studentExperience.map((phase, index) => (
            <Step key={index} active={true}>
              <StepLabel
                sx={{
                  '& .MuiStepLabel-label': {
                    fontSize: '1.2rem',
                    fontWeight: 600,
                    color: '#1e293b',
                  },
                }}
              >
                {phase.phase}
              </StepLabel>
              <StepContent>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#64748b',
                    mb: 2,
                    lineHeight: 1.6,
                  }}
                >
                  {phase.description}
                </Typography>
                <List dense>
                  {phase.activities.map((activity, activityIndex) => (
                    <ListItem key={activityIndex} sx={{ px: 0, py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 30 }}>
                        <CheckCircle sx={{ fontSize: '1rem', color: '#6a11cb' }} />
                      </ListItemIcon>
                      <ListItemText
                        primary={activity}
                        sx={{
                          '& .MuiListItemText-primary': {
                            fontSize: '0.9rem',
                            color: '#64748b',
                          },
                        }}
                      />
                    </ListItem>
                  ))}
                </List>
                {index < studentExperience.length - 1 && (
                  <Box sx={{ pb: 2 }}>
                    <Divider />
                  </Box>
                )}
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </Container>

      {/* Student Testimonials */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2600}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Student Voices
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Hear from our students about their transformative experiences at Nyanza TSS
          </Typography>

          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Grow in timeout={2800 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Avatar
                          src={testimonial.avatar}
                          sx={{
                            width: 60,
                            height: 60,
                            mr: 2,
                            border: '3px solid #6a11cb',
                          }}
                        />
                        <Box>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 600,
                              color: '#1e293b',
                              mb: 0.5,
                            }}
                          >
                            {testimonial.name}
                          </Typography>
                          <Typography variant="body2" color="#64748b" mb={0.5}>
                            {testimonial.program}
                          </Typography>
                          <Chip
                            label={testimonial.year}
                            size="small"
                            sx={{
                              bgcolor: '#6a11cb',
                              color: 'white',
                              fontWeight: 500,
                            }}
                          />
                        </Box>
                      </Box>

                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          lineHeight: 1.6,
                          fontStyle: 'italic',
                          mb: 3,
                        }}
                      >
                        "{testimonial.quote}"
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {[...Array(testimonial.rating)].map((_, starIndex) => (
                          <Star key={starIndex} sx={{ color: '#ffd700', fontSize: '1.2rem' }} />
                        ))}
                      </Box>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #6a11cb 0%, #2575fc 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={3000}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Ready to Join Our Community?
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Take the first step towards an exciting future in technical education.
            Discover what makes Nyanza TSS the perfect place for your academic journey.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                bgcolor: 'white',
                color: '#6a11cb',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Apply Now
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              Schedule Campus Visit
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default DiscoverPage
