import React, { useState } from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Button,
  Tabs,
  Tab,
  Avatar,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material'
import {
  Home,
  Build,
  Engineering,
  Electrical,
  Computer,
  DirectionsCar,
  Construction,
  CheckCircle,
  ExpandMore,
  Star,
  TrendingUp,
  Work,
  School,
  Timer,
  Group,
  EmojiEvents,
} from '@mui/icons-material'

const TechnicalSkillsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState(0)

  const skillCategories = [
    'All Skills',
    'Electronics & Telecommunications',
    'Automotive Technology',
    'Construction & Masonry',
    'Carpentry & Woodwork',
    'Solar Energy Systems'
  ]

  const technicalSkills = [
    {
      category: 'Electronics & Telecommunications',
      skills: [
        {
          name: 'Circuit Design & Analysis',
          level: 'Advanced',
          duration: '6 months',
          description: 'Design and analyze electronic circuits using industry-standard tools',
          tools: ['Multisim', 'Proteus', 'KiCad', 'Oscilloscope'],
          certification: 'IPC Certified',
          color: '#1976d2'
        },
        {
          name: 'PCB Design & Manufacturing',
          level: 'Intermediate',
          duration: '4 months',
          description: 'Create professional printed circuit boards from concept to production',
          tools: ['Altium Designer', 'Eagle CAD', 'Soldering Station'],
          certification: 'IPC-A-610 Certified',
          color: '#2e7d32'
        },
        {
          name: 'Telecommunications Systems',
          level: 'Advanced',
          duration: '8 months',
          description: 'Install and maintain modern communication networks',
          tools: ['Fiber Optic Tools', 'Network Analyzers', 'RF Meters'],
          certification: 'Cisco CCNA',
          color: '#ed6c02'
        },
        {
          name: 'Microcontroller Programming',
          level: 'Intermediate',
          duration: '5 months',
          description: 'Program embedded systems using Arduino and PIC microcontrollers',
          tools: ['Arduino IDE', 'MPLAB', 'Logic Analyzers'],
          certification: 'Embedded Systems Certificate',
          color: '#9c27b0'
        }
      ]
    },
    {
      category: 'Automotive Technology',
      skills: [
        {
          name: 'Engine Diagnostics & Repair',
          level: 'Advanced',
          duration: '10 months',
          description: 'Diagnose and repair modern automotive engines',
          tools: ['OBD Scanners', 'Engine Analyzers', 'Compression Testers'],
          certification: 'ASE Certified Technician',
          color: '#d32f2f'
        },
        {
          name: 'Automotive Electronics',
          level: 'Intermediate',
          duration: '6 months',
          description: 'Service electronic systems in modern vehicles',
          tools: ['Multimeters', 'Oscilloscopes', 'ECU Programmers'],
          certification: 'Automotive Electronics Certificate',
          color: '#1976d2'
        },
        {
          name: 'Hybrid Vehicle Technology',
          level: 'Advanced',
          duration: '8 months',
          description: 'Maintain and repair hybrid and electric vehicles',
          tools: ['High Voltage Meters', 'Battery Analyzers', 'Safety Equipment'],
          certification: 'Hybrid Vehicle Specialist',
          color: '#4caf50'
        },
        {
          name: 'Transmission Systems',
          level: 'Intermediate',
          duration: '7 months',
          description: 'Service manual and automatic transmission systems',
          tools: ['Transmission Jacks', 'Pressure Gauges', 'Scan Tools'],
          certification: 'Transmission Specialist',
          color: '#ff9800'
        }
      ]
    },
    {
      category: 'Construction & Masonry',
      skills: [
        {
          name: 'Concrete Technology',
          level: 'Advanced',
          duration: '6 months',
          description: 'Mix, pour, and finish concrete for various applications',
          tools: ['Concrete Mixers', 'Vibrators', 'Finishing Tools'],
          certification: 'ACI Concrete Technician',
          color: '#795548'
        },
        {
          name: 'Masonry & Bricklaying',
          level: 'Intermediate',
          duration: '8 months',
          description: 'Build walls, foundations, and decorative structures',
          tools: ['Trowels', 'Levels', 'Mortar Mixers'],
          certification: 'Masonry Craftsman',
          color: '#607d8b'
        },
        {
          name: 'Road Construction',
          level: 'Advanced',
          duration: '12 months',
          description: 'Build and maintain roads using modern techniques',
          tools: ['Compactors', 'Pavers', 'Survey Equipment'],
          certification: 'Road Construction Specialist',
          color: '#424242'
        }
      ]
    },
    {
      category: 'Carpentry & Woodwork',
      skills: [
        {
          name: 'Furniture Making',
          level: 'Advanced',
          duration: '10 months',
          description: 'Design and build custom furniture pieces',
          tools: ['Table Saws', 'Routers', 'Sanders', 'Jointers'],
          certification: 'Master Carpenter',
          color: '#8d6e63'
        },
        {
          name: 'Cabinet Installation',
          level: 'Intermediate',
          duration: '6 months',
          description: 'Install kitchen and bathroom cabinets professionally',
          tools: ['Drills', 'Levels', 'Measuring Tools'],
          certification: 'Cabinet Installer',
          color: '#5d4037'
        },
        {
          name: 'Interior Design Carpentry',
          level: 'Intermediate',
          duration: '8 months',
          description: 'Create custom interior woodwork and trim',
          tools: ['Miter Saws', 'Nail Guns', 'Chisels'],
          certification: 'Interior Carpentry Specialist',
          color: '#6d4c41'
        }
      ]
    },
    {
      category: 'Solar Energy Systems',
      skills: [
        {
          name: 'Solar Panel Installation',
          level: 'Advanced',
          duration: '6 months',
          description: 'Install residential and commercial solar systems',
          tools: ['Multimeters', 'Torque Wrenches', 'Safety Harnesses'],
          certification: 'NABCEP PV Installation Professional',
          color: '#ff9800'
        },
        {
          name: 'Solar System Design',
          level: 'Intermediate',
          duration: '4 months',
          description: 'Design efficient solar energy systems',
          tools: ['PVsyst Software', 'Solar Pathfinders', 'Irradiance Meters'],
          certification: 'Solar Design Specialist',
          color: '#ffc107'
        },
        {
          name: 'Battery Storage Systems',
          level: 'Advanced',
          duration: '5 months',
          description: 'Install and maintain energy storage solutions',
          tools: ['Battery Analyzers', 'Inverters', 'Monitoring Systems'],
          certification: 'Energy Storage Professional',
          color: '#4caf50'
        }
      ]
    }
  ]

  const filteredSkills = selectedCategory === 0 
    ? technicalSkills.flatMap(cat => cat.skills.map(skill => ({...skill, category: cat.category})))
    : technicalSkills.find(cat => cat.category === skillCategories[selectedCategory])?.skills || []

  const workshopFacilities = [
    {
      name: 'Electronics Workshop',
      description: 'Fully equipped with modern testing and measurement equipment',
      equipment: ['Digital Oscilloscopes', 'Function Generators', 'Power Supplies', 'Component Analyzers'],
      capacity: '30 students',
      image: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop'
    },
    {
      name: 'Automotive Bay',
      description: 'Professional automotive service facility with lifts and diagnostic tools',
      equipment: ['Vehicle Lifts', 'Diagnostic Scanners', 'Wheel Alignment', 'Engine Analyzers'],
      capacity: '20 students',
      image: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=400&h=300&fit=crop'
    },
    {
      name: 'Construction Lab',
      description: 'Hands-on facility for masonry and concrete work',
      equipment: ['Concrete Mixers', 'Testing Equipment', 'Hand Tools', 'Safety Gear'],
      capacity: '25 students',
      image: 'https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=400&h=300&fit=crop'
    },
    {
      name: 'Carpentry Workshop',
      description: 'Traditional and modern woodworking tools and machinery',
      equipment: ['Table Saws', 'Band Saws', 'Planers', 'Sanders', 'Hand Tools'],
      capacity: '20 students',
      image: 'https://images.unsplash.com/photo-*************-3373a0480b5b?w=400&h=300&fit=crop'
    }
  ]

  const industryPartners = [
    {
      name: 'Rwanda Energy Group',
      sector: 'Energy & Utilities',
      partnership: 'Solar installation training and internships',
      logo: '⚡'
    },
    {
      name: 'Cogebanque',
      sector: 'Banking & Finance',
      partnership: 'IT infrastructure and networking projects',
      logo: '🏦'
    },
    {
      name: 'Akagera Motors',
      sector: 'Automotive',
      partnership: 'Vehicle maintenance and repair training',
      logo: '🚗'
    },
    {
      name: 'Horizon Construction',
      sector: 'Construction',
      partnership: 'Road construction and masonry projects',
      logo: '🏗️'
    }
  ]

  const achievements = [
    {
      title: 'Skills Competition Winners',
      description: '5 students won national technical skills competitions in 2024',
      icon: <EmojiEvents />,
      color: '#ffd700'
    },
    {
      title: 'Industry Certification Rate',
      description: '92% of students achieve industry certifications',
      icon: <Star />,
      color: '#4caf50'
    },
    {
      title: 'Employment Success',
      description: '88% employment rate within 3 months of graduation',
      icon: <TrendingUp />,
      color: '#2196f3'
    },
    {
      title: 'Industry Partnerships',
      description: 'Active partnerships with 25+ local companies',
      icon: <Group />,
      color: '#ff9800'
    }
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Background */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 30% 70%, rgba(255, 107, 53, 0.3) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(247, 147, 30, 0.3) 0%, transparent 50%)',
            animation: 'float 16s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-16px) rotate(1deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <Build sx={{ mr: 0.5 }} fontSize="inherit" />
              Technical Skills
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #fff8f0 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Technical Skills Training
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Master practical technical skills through hands-on training in our state-of-the-art workshops. 
            From electronics to automotive technology, build expertise that industry demands.
          </Typography>
        </Container>
      </Box>

      {/* Achievement Stats */}
      <Container maxWidth="lg" sx={{ py: { xs: 4, md: 6 } }}>
        <Grid container spacing={3}>
          {achievements.map((achievement, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={1200 + index * 100}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${achievement.color}10 0%, ${achievement.color}05 100%)`,
                    borderRadius: 3,
                    border: `2px solid ${achievement.color}30`,
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: `0 20px 60px ${achievement.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: achievement.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        boxShadow: `0 8px 25px ${achievement.color}40`,
                      }}
                    >
                      {achievement.icon}
                    </Box>
                    <Typography variant="h6" fontWeight={600} color="#1e293b" mb={1}>
                      {achievement.title}
                    </Typography>
                    <Typography variant="body2" color="#64748b" lineHeight={1.5}>
                      {achievement.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Skills Categories */}
      <Container maxWidth="lg" sx={{ mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
          <Tabs
            value={selectedCategory}
            onChange={(e, newValue) => setSelectedCategory(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem',
                minWidth: 'auto',
                px: 3,
                py: 2,
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 107, 53, 0.1)',
                  transform: 'translateY(-2px)',
                },
              },
              '& .Mui-selected': {
                color: '#ff6b35 !important',
                bgcolor: 'rgba(255, 107, 53, 0.1)',
              },
              '& .MuiTabs-indicator': {
                height: 3,
                borderRadius: 2,
                bgcolor: '#ff6b35',
              },
            }}
          >
            {skillCategories.map((category, index) => (
              <Tab key={index} label={category} />
            ))}
          </Tabs>
        </Box>
      </Container>

      {/* Skills Grid */}
      <Container maxWidth="lg" sx={{ pb: { xs: 6, md: 8 } }}>
        <Grid container spacing={4}>
          {filteredSkills.map((skill, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Grow in timeout={1600 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-12px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Box
                        sx={{
                          width: 50,
                          height: 50,
                          bgcolor: skill.color,
                          borderRadius: 2,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          mr: 2,
                          boxShadow: `0 4px 15px ${skill.color}40`,
                        }}
                      >
                        <Build />
                      </Box>
                      <Box>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 600,
                            color: '#1e293b',
                            mb: 0.5,
                          }}
                        >
                          {skill.name}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Chip
                            label={skill.level}
                            size="small"
                            sx={{
                              bgcolor: skill.color,
                              color: 'white',
                              fontWeight: 500,
                              fontSize: '0.75rem',
                            }}
                          />
                          <Chip
                            label={skill.duration}
                            size="small"
                            variant="outlined"
                            sx={{
                              borderColor: skill.color,
                              color: skill.color,
                              fontWeight: 500,
                              fontSize: '0.75rem',
                            }}
                          />
                        </Box>
                      </Box>
                    </Box>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        mb: 3,
                        lineHeight: 1.6,
                      }}
                    >
                      {skill.description}
                    </Typography>

                    <Box sx={{ mb: 3 }}>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                        }}
                      >
                        Tools & Equipment:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {skill.tools.map((tool, toolIndex) => (
                          <Chip
                            key={toolIndex}
                            label={tool}
                            size="small"
                            variant="outlined"
                            sx={{
                              fontSize: '0.7rem',
                              height: 24,
                            }}
                          />
                        ))}
                      </Box>
                    </Box>

                    <Box sx={{ mb: 3 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          color: '#64748b',
                        }}
                      >
                        <CheckCircle sx={{ fontSize: '1rem', mr: 1, color: skill.color }} />
                        <strong>Certification:</strong> {skill.certification}
                      </Typography>
                    </Box>

                    <Button
                      variant="contained"
                      fullWidth
                      sx={{
                        bgcolor: skill.color,
                        color: 'white',
                        fontWeight: 600,
                        py: 1.5,
                        borderRadius: 2,
                        textTransform: 'none',
                        '&:hover': {
                          bgcolor: skill.color,
                          filter: 'brightness(0.9)',
                        },
                      }}
                    >
                      Learn More
                    </Button>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Workshop Facilities */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2000}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Workshop Facilities
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Train in professional-grade workshops equipped with industry-standard tools and equipment
          </Typography>

          <Grid container spacing={4}>
            {workshopFacilities.map((facility, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Grow in timeout={2200 + index * 200}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      overflow: 'hidden',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <Box
                      sx={{
                        height: 200,
                        backgroundImage: `url(${facility.image})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        position: 'relative',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: 'linear-gradient(45deg, rgba(255, 107, 53, 0.8) 0%, rgba(247, 147, 30, 0.8) 100%)',
                        },
                      }}
                    >
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 16,
                          right: 16,
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          borderRadius: 2,
                          px: 2,
                          py: 1,
                        }}
                      >
                        <Typography variant="body2" fontWeight={600} color="#1e293b">
                          Capacity: {facility.capacity}
                        </Typography>
                      </Box>
                    </Box>

                    <CardContent sx={{ p: 4 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          mb: 2,
                          color: '#1e293b',
                        }}
                      >
                        {facility.name}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          mb: 3,
                          lineHeight: 1.6,
                        }}
                      >
                        {facility.description}
                      </Typography>

                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                        }}
                      >
                        Equipment Available:
                      </Typography>

                      <List dense>
                        {facility.equipment.map((item, itemIndex) => (
                          <ListItem key={itemIndex} sx={{ px: 0, py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 30 }}>
                              <CheckCircle sx={{ fontSize: '1rem', color: '#ff6b35' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={item}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.85rem',
                                  color: '#64748b',
                                },
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Industry Partners */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2400}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Industry Partners
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Learn from real-world projects through partnerships with leading companies
        </Typography>

        <Grid container spacing={4}>
          {industryPartners.map((partner, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={2600 + index * 200}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Typography variant="h2" sx={{ mb: 2 }}>
                      {partner.logo}
                    </Typography>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 1,
                        color: '#1e293b',
                      }}
                    >
                      {partner.name}
                    </Typography>
                    <Chip
                      label={partner.sector}
                      size="small"
                      sx={{
                        bgcolor: '#ff6b35',
                        color: 'white',
                        fontWeight: 500,
                        mb: 2,
                      }}
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                      }}
                    >
                      {partner.partnership}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={2800}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Build Your Technical Expertise
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Join our technical skills programs and gain the hands-on experience that employers value.
            Start your journey to becoming a skilled technician today.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                bgcolor: 'white',
                color: '#ff6b35',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Enroll in Skills Training
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              Visit Our Workshops
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default TechnicalSkillsPage
