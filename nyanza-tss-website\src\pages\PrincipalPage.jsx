import React from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  Avatar,
  Divider,
} from '@mui/material'
import {
  Home,
  School,
  Person,
  Quote,
  EmojiEvents,
  Groups,
  TrendingUp,
  Star,
} from '@mui/icons-material'

const PrincipalPage = () => {
  const principalQuotes = [
    {
      text: "Education is the most powerful weapon which you can use to change the world. At Nyanza TSS, we are committed to empowering our students with the technical skills and knowledge they need to transform not only their own lives but also contribute meaningfully to Rwanda's development.",
      context: "On Education Philosophy"
    },
    {
      text: "Our students' achievements in national examinations and sports competitions are a testament to the dedication of our faculty and the potential of our young people. We will continue to strive for excellence in all our endeavors.",
      context: "On Student Achievement"
    },
    {
      text: "Technical and vocational education is the backbone of any developing economy. We are proud to be preparing the skilled workforce that Rwanda needs for its continued growth and prosperity.",
      context: "On TVET Education"
    }
  ]

  const achievements = [
    {
      title: "National Volleyball Championship",
      description: "Led the school to multiple national volleyball championships",
      icon: <EmojiEvents />,
      color: "#ffd700",
    },
    {
      title: "Academic Excellence",
      description: "Oversaw students ranking 5th nationally in Industrial Electronics",
      icon: <Star />,
      color: "#4ecdc4",
    },
    {
      title: "Program Development",
      description: "Expanded TVET programs to 12 specialized technical courses",
      icon: <TrendingUp />,
      color: "#45b7d1",
    },
    {
      title: "Community Leadership",
      description: "Strengthened partnerships with local industries and communities",
      icon: <Groups />,
      color: "#ff6b35",
    },
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.3) 0%, transparent 50%)',
            animation: 'float 20s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px)' },
              '50%': { transform: 'translateY(-20px)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <Person sx={{ mr: 0.5 }} fontSize="inherit" />
              Principal's Message
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
              }}
            >
              Principal's Message
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
            }}
          >
            A message from our principal, Manirambona Leonard, about our commitment to excellence 
            in technical and vocational education at Nyanza TSS.
          </Typography>
        </Container>
      </Box>

      {/* Principal Profile Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Grid container spacing={6} alignItems="center">
          <Grid item xs={12} md={4}>
            <Fade in timeout={1200}>
              <Card
                sx={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(20px)',
                  borderRadius: 4,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  overflow: 'hidden',
                  position: 'relative',
                }}
              >
                {/* Decorative Header */}
                <Box
                  sx={{
                    height: 80,
                    background: 'linear-gradient(135deg, #ffd700 0%, #ff9800 100%)',
                    position: 'relative',
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: 20,
                      background: 'rgba(255, 255, 255, 0.95)',
                      borderRadius: '50% 50% 0 0 / 100% 100% 0 0',
                    },
                  }}
                />

                <CardContent sx={{ p: 4, textAlign: 'center', mt: -5 }}>
                  <Avatar
                    sx={{
                      width: 120,
                      height: 120,
                      mx: 'auto',
                      mb: 3,
                      border: '4px solid white',
                      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                      fontSize: '3rem',
                      bgcolor: '#1976d2',
                    }}
                  >
                    ML
                  </Avatar>

                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 700,
                      mb: 1,
                      color: '#1e293b',
                      fontSize: { xs: '1.5rem', md: '1.8rem' },
                    }}
                  >
                    Manirambona Leonard
                  </Typography>

                  <Typography
                    variant="h6"
                    sx={{
                      color: '#64748b',
                      mb: 2,
                      fontWeight: 500,
                    }}
                  >
                    Principal
                  </Typography>

                  <Typography
                    variant="body2"
                    sx={{
                      color: '#64748b',
                      mb: 3,
                      lineHeight: 1.6,
                    }}
                  >
                    Nyanza Technical Secondary School
                  </Typography>

                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: 1,
                      p: 2,
                      bgcolor: '#f8fafc',
                      borderRadius: 2,
                    }}
                  >
                    <School sx={{ color: '#1976d2', fontSize: '1.2rem' }} />
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#1e293b',
                        fontWeight: 600,
                      }}
                    >
                      Contact: 0788309436
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Fade>
          </Grid>

          <Grid item xs={12} md={8}>
            <Fade in timeout={1400}>
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 700,
                  mb: 4,
                  color: '#1e293b',
                  fontSize: { xs: '2rem', md: '2.5rem' },
                }}
              >
                Welcome to Nyanza TSS
              </Typography>
            </Fade>

            <Typography
              variant="body1"
              sx={{
                color: '#64748b',
                fontSize: '1.1rem',
                lineHeight: 1.8,
                mb: 4,
              }}
            >
              Dear Students, Parents, and Stakeholders,
            </Typography>

            <Typography
              variant="body1"
              sx={{
                color: '#64748b',
                fontSize: '1.1rem',
                lineHeight: 1.8,
                mb: 4,
              }}
            >
              It is with great pride and enthusiasm that I welcome you to Nyanza Technical Secondary School. 
              As the principal of this esteemed institution, I am honored to lead a school that has consistently 
              demonstrated excellence in technical and vocational education training.
            </Typography>

            <Typography
              variant="body1"
              sx={{
                color: '#64748b',
                fontSize: '1.1rem',
                lineHeight: 1.8,
                mb: 4,
              }}
            >
              Located in the beautiful Nyanza District, our school has become a beacon of hope and opportunity 
              for young people seeking quality technical education. We offer comprehensive TVET programs from 
              Levels 3 to 5, covering 12 specialized areas that are crucial for Rwanda's economic development.
            </Typography>

            <Typography
              variant="body1"
              sx={{
                color: '#64748b',
                fontSize: '1.1rem',
                lineHeight: 1.8,
              }}
            >
              Our recent achievements, including our students ranking 5th nationally in Industrial Electronics 
              and 10th in Transport & Logistics, along with our volleyball team's national championship, 
              demonstrate our commitment to excellence in both academics and extracurricular activities.
            </Typography>
          </Grid>
        </Grid>
      </Container>

      {/* Quotes Section */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={1600}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 6,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Words of Wisdom
            </Typography>
          </Fade>

          <Grid container spacing={4}>
            {principalQuotes.map((quote, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Grow in timeout={1800 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.9)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      <Quote
                        sx={{
                          fontSize: '2rem',
                          color: '#1976d2',
                          mb: 2,
                        }}
                      />
                      <Typography
                        variant="body1"
                        sx={{
                          color: '#64748b',
                          fontStyle: 'italic',
                          lineHeight: 1.7,
                          mb: 3,
                        }}
                      >
                        "{quote.text}"
                      </Typography>
                      <Divider sx={{ mb: 2 }} />
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#1976d2',
                          fontWeight: 600,
                        }}
                      >
                        {quote.context}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Leadership Achievements */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2000}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 6,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Leadership Achievements
          </Typography>
        </Fade>

        <Grid container spacing={4}>
          {achievements.map((achievement, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={2200 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: achievement.color,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        fontSize: '1.5rem',
                      }}
                    >
                      {achievement.icon}
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        color: '#1e293b',
                        fontSize: '1.1rem',
                      }}
                    >
                      {achievement.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                      }}
                    >
                      {achievement.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  )
}

export default PrincipalPage
