import React from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
} from '@mui/material'
import {
  Home,
  School,
  EmojiEvents,
  Groups,
  LocationOn,
  Phone,
  Email,
  Star,
  TrendingUp,
  Engineering,
} from '@mui/icons-material'

const AboutPage = () => {
  const achievements = [
    {
      title: "National Volleyball Champions",
      description: "Our volleyball team has consistently dominated national competitions, bringing home championship trophies.",
      icon: <EmojiEvents />,
      color: "#ffd700",
    },
    {
      title: "5th Place - Industrial Electronics",
      description: "Students ranked 5th nationally in Industrial Electronics in 2024 NESA TVET examinations.",
      icon: <Star />,
      color: "#4ecdc4",
    },
    {
      title: "10th Place - Transport & Logistics",
      description: "Transport & Logistics program achieved 10th position nationally in TVET examinations.",
      icon: <TrendingUp />,
      color: "#45b7d1",
    },
    {
      title: "TVET Excellence Recognition",
      description: "Recognized for excellence in technical and vocational education training across multiple programs.",
      icon: <Engineering />,
      color: "#ff6b35",
    },
  ]

  const schoolInfo = [
    {
      title: "Location",
      content: "Kigoma Sector, Butansinda Cell, Nyanza District, Southern Province, Rwanda",
      icon: <LocationOn />,
      color: "#2196f3",
    },
    {
      title: "Principal",
      content: "Manirambona Leonard",
      icon: <Groups />,
      color: "#4caf50",
    },
    {
      title: "Contact",
      content: "0788309436",
      icon: <Phone />,
      color: "#ff9800",
    },
    {
      title: "Programs",
      content: "TVET Levels 3-5 in 12 specialized programs",
      icon: <School />,
      color: "#9c27b0",
    },
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.3) 0%, transparent 50%)',
            animation: 'float 20s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px)' },
              '50%': { transform: 'translateY(-20px)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <School sx={{ mr: 0.5 }} fontSize="inherit" />
              About Nyanza TSS
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
              }}
            >
              About Nyanza Technical Secondary School
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
            }}
          >
            Excellence in Technical and Vocational Education Training since our establishment. 
            Located in the heart of Nyanza District, we are committed to developing skilled professionals 
            for Rwanda's growing economy.
          </Typography>
        </Container>
      </Box>

      {/* School Information Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={1200}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 6,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            School Information
          </Typography>
        </Fade>

        <Grid container spacing={4}>
          {schoolInfo.map((info, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={1400 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: info.color,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        fontSize: '1.5rem',
                      }}
                    >
                      {info.icon}
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 1,
                        color: '#1e293b',
                      }}
                    >
                      {info.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                      }}
                    >
                      {info.content}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Mission & Vision Section */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Fade in timeout={1600}>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 700,
                    mb: 3,
                    color: '#1e293b',
                    fontSize: { xs: '2rem', md: '2.5rem' },
                  }}
                >
                  Our Mission
                </Typography>
              </Fade>
              <Typography
                variant="body1"
                sx={{
                  color: '#64748b',
                  fontSize: '1.1rem',
                  lineHeight: 1.8,
                  mb: 4,
                }}
              >
                To provide quality technical and vocational education that equips students with practical skills, 
                knowledge, and competencies needed to contribute meaningfully to Rwanda's socio-economic development. 
                We are committed to fostering innovation, entrepreneurship, and excellence in all our programs.
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Fade in timeout={1800}>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 700,
                    mb: 3,
                    color: '#1e293b',
                    fontSize: { xs: '2rem', md: '2.5rem' },
                  }}
                >
                  Our Vision
                </Typography>
              </Fade>
              <Typography
                variant="body1"
                sx={{
                  color: '#64748b',
                  fontSize: '1.1rem',
                  lineHeight: 1.8,
                }}
              >
                To be a leading technical secondary school in Rwanda, recognized for excellence in TVET education, 
                producing skilled graduates who drive technological advancement and economic growth in our nation. 
                We envision a future where our students become leaders and innovators in their chosen fields.
              </Typography>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Achievements Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2000}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 6,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Our Achievements
          </Typography>
        </Fade>

        <Grid container spacing={4}>
          {achievements.map((achievement, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={2200 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: achievement.color,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        fontSize: '1.5rem',
                      }}
                    >
                      {achievement.icon}
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        color: '#1e293b',
                        fontSize: '1.1rem',
                      }}
                    >
                      {achievement.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                      }}
                    >
                      {achievement.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  )
}

export default AboutPage
