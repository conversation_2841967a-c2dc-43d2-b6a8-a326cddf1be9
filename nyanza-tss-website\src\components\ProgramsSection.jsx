import { useState } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Button,
  Fade,
  Zoom,
} from '@mui/material'
import {
  Computer,
  ElectricalServices,
  DirectionsCar,
  ArrowForward,
} from '@mui/icons-material'

const ProgramsSection = () => {
  const [hoveredCard, setHoveredCard] = useState(null)

  const programs = [
    {
      id: 1,
      title: 'L3-L5 Computer System and Architecture',
      description: 'Master computer systems, programming, and 3D printing technology. Learn hardware design, software development, and advanced manufacturing techniques.',
      image: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=500&h=300&fit=crop',
      icon: <Computer />,
      color: '#2196f3',
      features: ['Computer Programming', 'System Architecture', '3D Printing', 'Hardware Design'],
    },
    {
      id: 2,
      title: 'L3-L5 Electronics and Telecommunication',
      description: 'Explore electronics, circuit design, and telecommunication systems. Hands-on experience with soldering, signal processing, and communication networks.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=500&h=300&fit=crop',
      icon: <ElectricalServices />,
      color: '#ff9800',
      features: ['Circuit Design', 'Soldering Techniques', 'Telecommunication', 'Signal Processing'],
    },
    {
      id: 3,
      title: 'L3-L5 Automobile Technology',
      description: 'Learn automotive engineering, vehicle maintenance, and modern car manufacturing. Practical training in automotive systems and repair techniques.',
      image: 'https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?w=500&h=300&fit=crop',
      icon: <DirectionsCar />,
      color: '#4caf50',
      features: ['Engine Systems', 'Vehicle Maintenance', 'Automotive Electronics', 'Manufacturing'],
    },
  ]

  return (
    <Box
      id="programs"
      sx={{
        py: 10,
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(33, 150, 243, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(255, 152, 0, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(76, 175, 80, 0.05) 0%, transparent 50%)
          `,
          zIndex: 1,
        }
      }}
    >
      {/* Background Elements */}
      {[...Array(6)].map((_, i) => (
        <Box
          key={i}
          sx={{
            position: 'absolute',
            width: i % 2 === 0 ? 100 : 60,
            height: i % 2 === 0 ? 100 : 60,
            background: i % 3 === 0
              ? 'linear-gradient(45deg, rgba(33, 150, 243, 0.08), rgba(33, 150, 243, 0.02))'
              : i % 3 === 1
              ? 'linear-gradient(45deg, rgba(255, 152, 0, 0.08), rgba(255, 152, 0, 0.02))'
              : 'linear-gradient(45deg, rgba(76, 175, 80, 0.08), rgba(76, 175, 80, 0.02))',
            borderRadius: i % 2 === 0 ? '50%' : '20%',
            top: `${Math.random() * 80 + 10}%`,
            left: `${Math.random() * 80 + 10}%`,
            animation: `programFloat ${Math.random() * 8 + 6}s ease-in-out infinite alternate`,
            '@keyframes programFloat': {
              '0%': { transform: 'translateY(0px) scale(1)' },
              '100%': { transform: 'translateY(-20px) scale(1.05)' },
            },
          }}
        />
      ))}

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography
              variant="h2"
              sx={{
                color: '#2c3e50',
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2.2rem', sm: '2.8rem', md: '3.2rem' },
                textShadow: '1px 1px 2px rgba(0,0,0,0.1)',
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -10,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 4,
                  background: 'linear-gradient(90deg, #2196f3, #ff9800, #4caf50)',
                  borderRadius: 2,
                },
              }}
            >
              Our Technical Programs
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#546e7a',
                maxWidth: '700px',
                mx: 'auto',
                fontSize: { xs: '1.1rem', sm: '1.3rem' },
                fontWeight: 400,
                lineHeight: 1.6,
                mt: 3,
              }}
            >
              Discover our comprehensive technical education programs designed to prepare you for success in today's technology-driven world
            </Typography>
          </Box>
        </Fade>

        {/* Programs Grid */}
        <Grid container spacing={4} justifyContent="center">
          {programs.map((program, index) => (
            <Grid item xs={12} sm={6 } md={4} key={program.id}>
              <Zoom
                in
                timeout={800 + index * 200}
                style={{ transitionDelay: `${index * 200}ms` }}
              >
                <Card
                  onMouseEnter={() => setHoveredCard(program.id)}
                  onMouseLeave={() => setHoveredCard(null)}
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: 4,
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                    '&:hover': {
                      transform: 'translateY(-15px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2)',
                    },
                  }}
                >
                  {/* Program Image */}
                  <CardMedia
                    component="img"
                    height="200"
                    image={program.image}
                    alt={program.title}
                    sx={{
                      transition: 'transform 0.4s ease',
                      transform: hoveredCard === program.id ? 'scale(1.1)' : 'scale(1)',
                    }}
                  />

                  {/* Program Icon */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 15,
                      right: 15,
                      width: 50,
                      height: 50,
                      bgcolor: program.color,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
                      transition: 'all 0.3s ease',
                      transform: hoveredCard === program.id ? 'scale(1.1) rotate(10deg)' : 'scale(1)',
                    }}
                  >
                    {program.icon}
                  </Box>

                  <CardContent sx={{ p: 3 }}>
                    {/* Program Title */}
                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 700,
                        mb: 2,
                        color: '#2c3e50',
                        fontSize: '1.3rem',
                        lineHeight: 1.3,
                        transition: 'color 0.3s ease',
                        '&:hover': {
                          color: program.color,
                        },
                      }}
                    >
                      {program.title}
                    </Typography>

                    {/* Program Description */}
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#546e7a',
                        fontSize: '0.95rem',
                        lineHeight: 1.6,
                        mb: 3,
                      }}
                    >
                      {program.description}
                    </Typography>

                    {/* Program Features */}
                    <Box sx={{ mb: 3 }}>
                      {program.features.map((feature, idx) => (
                        <Typography
                          key={idx}
                          variant="body2"
                          sx={{
                            color: program.color,
                            fontSize: '0.85rem',
                            fontWeight: 500,
                            mb: 0.5,
                            '&::before': {
                              content: '"•"',
                              marginRight: 1,
                              fontWeight: 'bold',
                            },
                          }}
                        >
                          {feature}
                        </Typography>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>

        {/* View More Button */}
        <Box sx={{ textAlign: 'center', mt: 6 }}>
          <Button
            variant="contained"
            size="large"
            endIcon={<ArrowForward />}
            sx={{
              bgcolor: '#2c3e50',
              color: 'white',
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600,
              borderRadius: 3,
              boxShadow: '0 8px 25px rgba(44, 62, 80, 0.3)',
              transition: 'all 0.3s ease',
              '&:hover': {
                bgcolor: '#34495e',
                transform: 'translateY(-3px)',
                boxShadow: '0 12px 35px rgba(44, 62, 80, 0.4)',
              },
            }}
          >
            View More Programs
          </Button>
        </Box>
      </Container>
    </Box>
  )
}

export default ProgramsSection
