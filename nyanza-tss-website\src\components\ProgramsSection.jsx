import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  Fade,
} from '@mui/material'
import {
  ElectricalServices,
  DirectionsCar,
  ArrowForward,
  EmojiEvents,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

const ProgramsSection = () => {
  const navigate = useNavigate()

  const programs = [
    {
      id: 1,
      title: 'Electronics & Telecommunications',
      shortTitle: 'E&T',
      description: 'Advanced electronics and telecommunication systems with national excellence recognition.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop',
      icon: <ElectricalServices />,
      color: '#2196f3',
      level: 'L3-L5',
      duration: '3 Years',
      achievement: true,
    },
    {
      id: 2,
      title: 'Electrical & Industrial Electricity',
      shortTitle: 'EIE',
      description: 'Comprehensive electrical systems and industrial electricity training.',
      image: 'https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=400&h=200&fit=crop',
      icon: <ElectricalServices />,
      color: '#ff9800',
      level: 'L3-L5',
      duration: '3 Years',
    },
    {
      id: 3,
      title: 'Auto Mechanics',
      shortTitle: 'AUTO',
      description: 'Complete automotive repair and maintenance training program.',
      image: 'https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?w=400&h=200&fit=crop',
      icon: <DirectionsCar />,
      color: '#4caf50',
      level: 'L3-L5',
      duration: '3 Years',
    },
  ]

  return (
    <Box
      id="programs"
      sx={{
        py: { xs: 6, md: 8 },
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Subtle Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 80%, rgba(33, 150, 243, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 152, 0, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(76, 175, 80, 0.03) 0%, transparent 50%)
          `,
          zIndex: 1,
        }}
      />

      <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 2 }}>
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: { xs: 4, md: 6 } }}>
            <Typography
              variant="h2"
              sx={{
                color: '#1e293b',
                fontWeight: 800,
                mb: 2,
                fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.8rem' },
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -8,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 60,
                  height: 3,
                  background: 'linear-gradient(90deg, #2196f3, #ff9800, #4caf50)',
                  borderRadius: 2,
                },
              }}
            >
              Our TVET Programs
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#64748b',
                maxWidth: '600px',
                mx: 'auto',
                fontSize: { xs: '1rem', sm: '1.1rem' },
                fontWeight: 400,
                lineHeight: 1.6,
                mt: 3,
              }}
            >
              Nyanza Technical Secondary School - Level 3-5 TVET certifications with national excellence recognition
            </Typography>
          </Box>
        </Fade>

        {/* Programs Horizontal Layout */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            gap: { xs: 3, md: 4 },
            justifyContent: 'center',
            alignItems: 'stretch',
          }}
        >
          {programs.map((program, index) => (
            <Fade
              key={program.id}
              in
              timeout={800 + index * 200}
              style={{ transitionDelay: `${index * 200}ms` }}
            >
              <Card
                sx={{
                  flex: 1,
                  maxWidth: { xs: '100%', md: '350px' },
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 3,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                    '& .program-image': {
                      transform: 'scale(1.05)',
                    },
                    '& .program-icon': {
                      transform: 'scale(1.1) rotate(5deg)',
                    },
                  },
                }}
              >
                {/* Program Image */}
                <CardMedia
                  component="img"
                  height="140"
                  image={program.image}
                  alt={program.title}
                  className="program-image"
                  sx={{
                    transition: 'transform 0.3s ease',
                    objectFit: 'cover',
                  }}
                />

                {/* Program Icon */}
                <Box
                  className="program-icon"
                  sx={{
                    position: 'absolute',
                    top: 12,
                    right: 12,
                    width: 40,
                    height: 40,
                    bgcolor: program.color,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                    transition: 'all 0.3s ease',
                    fontSize: '1.2rem',
                  }}
                >
                  {program.icon}
                </Box>

                {/* Level Badge */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 12,
                    left: 12,
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                    color: program.color,
                    px: 1.5,
                    py: 0.5,
                    borderRadius: 2,
                    fontSize: '0.75rem',
                    fontWeight: 600,
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  }}
                >
                  {program.level}
                </Box>

                {/* Achievement Badge */}
                {program.achievement && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 50,
                      left: 12,
                      bgcolor: '#fef3c7',
                      color: '#92400e',
                      px: 1,
                      py: 0.3,
                      borderRadius: 2,
                      fontSize: '0.65rem',
                      fontWeight: 600,
                      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 0.5,
                    }}
                  >
                    <EmojiEvents sx={{ fontSize: '0.8rem' }} />
                    Excellence
                  </Box>
                )}

                <CardContent sx={{ p: 2.5 }}>
                  {/* Program Title */}
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      mb: 1,
                      color: '#1e293b',
                      fontSize: '1.1rem',
                      lineHeight: 1.3,
                      transition: 'color 0.3s ease',
                    }}
                  >
                    {program.title}
                  </Typography>

                  {/* Program Description */}
                  <Typography
                    variant="body2"
                    sx={{
                      color: '#64748b',
                      fontSize: '0.9rem',
                      lineHeight: 1.5,
                      mb: 2,
                    }}
                  >
                    {program.description}
                  </Typography>

                  {/* Duration Info */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      pt: 1,
                      borderTop: '1px solid rgba(0,0,0,0.05)',
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color: program.color,
                        fontWeight: 600,
                        fontSize: '0.85rem',
                      }}
                    >
                      Duration: {program.duration}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#94a3b8',
                        fontSize: '0.8rem',
                        fontWeight: 500,
                      }}
                    >
                      {program.shortTitle}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Fade>
          ))}
        </Box>

        {/* Call to Action */}
        <Box sx={{ textAlign: 'center', mt: { xs: 4, md: 6 } }}>
          <Button
            variant="contained"
            size="large"
            endIcon={<ArrowForward />}
            onClick={() => navigate('/programs')}
            sx={{
              bgcolor: '#1e293b',
              color: 'white',
              px: 3,
              py: 1.2,
              fontSize: '1rem',
              fontWeight: 600,
              borderRadius: 2,
              boxShadow: '0 4px 15px rgba(30, 41, 59, 0.2)',
              transition: 'all 0.3s ease',
              '&:hover': {
                bgcolor: '#334155',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(30, 41, 59, 0.3)',
              },
            }}
          >
            Explore All Programs
          </Button>
        </Box>
      </Container>
    </Box>
  )
}

export default ProgramsSection
