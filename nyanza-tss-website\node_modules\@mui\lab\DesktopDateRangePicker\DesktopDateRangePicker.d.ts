import * as React from 'react';
type DesktopDateRangePickerComponent = (<TDate>(props: DesktopDateRangePickerProps & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * @deprecated The DesktopDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.
 * @ignore - do not document.
 */
declare const DesktopDateRangePicker: DesktopDateRangePickerComponent;
export default DesktopDateRangePicker;
export type DesktopDateRangePickerProps = Record<any, any>;