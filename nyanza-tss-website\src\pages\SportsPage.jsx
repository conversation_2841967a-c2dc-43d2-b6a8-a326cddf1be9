import React, { useState } from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Breadc<PERSON>bs,
  Link,
  Fade,
  Grow,
  Chip,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  LinearProgress,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
  Tabs,
  Tab,
} from '@mui/material'
import {
  Home,
  SportsVolleyball,
  SportsBasketball,
  SportsSoccer,
  SportsHandball,
  DirectionsRun,
  FitnessCenter,
  EmojiEvents,
  Group,
  Star,
  CheckCircle,
  Schedule,
  LocationOn,
  Person,
  TrendingUp,
  Favorite,
  School,
} from '@mui/icons-material'

const SportsPage = () => {
  const [selectedSport, setSelectedSport] = useState(0)

  const sportsPrograms = [
    {
      name: 'Volleyball',
      description: 'Our championship volleyball team - National Champions 2023',
      image: 'https://images.unsplash.com/photo-1612872087720-bb876e2e67d1?w=400&h=300&fit=crop',
      icon: <SportsVolleyball />,
      color: '#4caf50',
      achievements: [
        'National Champions 2023',
        'Regional Tournament Winners 2022-2023',
        'Inter-School Champions 2021-2023',
        'Best Team Spirit Award 2023'
      ],
      teamSize: 18,
      trainingDays: 'Monday to Friday',
      trainingTime: '6:00 AM & 5:00 PM',
      coach: 'Coach Emmanuel Nzeyimana',
      facilities: ['Professional volleyball court', 'Training equipment', 'Team locker rooms'],
      upcomingEvents: [
        { date: 'March 15, 2024', event: 'Regional Championship' },
        { date: 'April 20, 2024', event: 'National Tournament' },
        { date: 'May 10, 2024', event: 'Inter-School Competition' }
      ]
    },
    {
      name: 'Basketball',
      description: 'Developing basketball skills and teamwork on the court',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=300&fit=crop',
      icon: <SportsBasketball />,
      color: '#ff9800',
      achievements: [
        'Regional Semi-finalists 2023',
        'Best Improvement Award 2023',
        'Fair Play Award 2022',
        'District Champions 2022'
      ],
      teamSize: 15,
      trainingDays: 'Tuesday, Thursday, Saturday',
      trainingTime: '4:00 PM',
      coach: 'Coach Marie Uwimana',
      facilities: ['Basketball court', 'Training balls', 'Scoreboard system'],
      upcomingEvents: [
        { date: 'March 25, 2024', event: 'District Tournament' },
        { date: 'April 15, 2024', event: 'Regional Qualifiers' },
        { date: 'May 5, 2024', event: 'Friendly Match vs Rivals' }
      ]
    },
    {
      name: 'Football (Soccer)',
      description: 'Building character and skills through the beautiful game',
      image: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=400&h=300&fit=crop',
      icon: <SportsSoccer />,
      color: '#2196f3',
      achievements: [
        'Regional Quarter-finalists 2023',
        'Best Goalkeeper Award 2023',
        'Most Goals Scored 2022',
        'Team Unity Award 2023'
      ],
      teamSize: 22,
      trainingDays: 'Monday, Wednesday, Friday',
      trainingTime: '5:30 PM',
      coach: 'Coach Jean Baptiste Habimana',
      facilities: ['Football field', 'Goal posts', 'Training cones and equipment'],
      upcomingEvents: [
        { date: 'March 30, 2024', event: 'League Match' },
        { date: 'April 12, 2024', event: 'Cup Competition' },
        { date: 'May 18, 2024', event: 'End of Season Tournament' }
      ]
    },
    {
      name: 'Athletics',
      description: 'Track and field events for speed, strength, and endurance',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
      icon: <DirectionsRun />,
      color: '#9c27b0',
      achievements: [
        'Regional 100m Champion 2023',
        'Long Jump Record Holder',
        'Relay Team Bronze Medal',
        'Most Improved Athlete 2023'
      ],
      teamSize: 25,
      trainingDays: 'Monday to Friday',
      trainingTime: '6:30 AM',
      coach: 'Coach Patrick Nkurunziza',
      facilities: ['Running track', 'Field event areas', 'Training equipment'],
      upcomingEvents: [
        { date: 'April 5, 2024', event: 'Regional Athletics Meet' },
        { date: 'May 15, 2024', event: 'National Championships' },
        { date: 'June 1, 2024', event: 'Inter-School Athletics' }
      ]
    }
  ]

  const sportsStats = [
    { label: 'Active Athletes', value: '120+', icon: <Person />, color: '#4caf50' },
    { label: 'Sports Programs', value: '8', icon: <SportsVolleyball />, color: '#2196f3' },
    { label: 'Championships Won', value: '15', icon: <EmojiEvents />, color: '#ffd700' },
    { label: 'Training Hours/Week', value: '25', icon: <Schedule />, color: '#ff9800' },
  ]

  const facilities = [
    {
      name: 'Volleyball Court',
      description: 'Professional-grade volleyball court with proper lighting',
      image: 'https://images.unsplash.com/photo-1612872087720-bb876e2e67d1?w=400&h=200&fit=crop',
      features: ['Professional net system', 'Proper court markings', 'Spectator seating', 'Equipment storage']
    },
    {
      name: 'Basketball Court',
      description: 'Full-size basketball court for training and competitions',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=200&fit=crop',
      features: ['Regulation hoops', 'Court lighting', 'Scoreboard', 'Team benches']
    },
    {
      name: 'Football Field',
      description: 'Large football field for matches and training sessions',
      image: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=400&h=200&fit=crop',
      features: ['Full-size field', 'Goal posts', 'Sideline markings', 'Training equipment']
    },
    {
      name: 'Athletics Track',
      description: 'Running track and field event areas for athletics training',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=200&fit=crop',
      features: ['400m track', 'Long jump pit', 'Shot put area', 'High jump setup']
    },
    {
      name: 'Fitness Center',
      description: 'Modern fitness equipment for strength and conditioning',
      image: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=200&fit=crop',
      features: ['Weight training equipment', 'Cardio machines', 'Free weights', 'Training mats']
    },
    {
      name: 'Locker Rooms',
      description: 'Clean and secure changing facilities for all athletes',
      image: 'https://images.unsplash.com/photo-1571902943202-507ec2618e8f?w=400&h=200&fit=crop',
      features: ['Individual lockers', 'Shower facilities', 'Bench seating', 'Equipment storage']
    }
  ]

  const achievements = [
    {
      year: '2023',
      title: 'National Volleyball Champions',
      description: 'Our volleyball team won the national championship, defeating top schools from across Rwanda',
      icon: <EmojiEvents />,
      color: '#ffd700'
    },
    {
      year: '2023',
      title: 'Regional Athletics Excellence',
      description: 'Multiple medals in regional athletics competitions, including record-breaking performances',
      icon: <DirectionsRun />,
      color: '#9c27b0'
    },
    {
      year: '2022',
      title: 'Best Sports Program Award',
      description: 'Recognized for outstanding sports program development and student athlete support',
      icon: <Star />,
      color: '#4caf50'
    },
    {
      year: '2022',
      title: 'Fair Play Champions',
      description: 'Awarded for exemplary sportsmanship and fair play across all sporting competitions',
      icon: <Favorite />,
      color: '#e91e63'
    }
  ]

  const coaches = [
    {
      name: 'Emmanuel Nzeyimana',
      sport: 'Volleyball',
      experience: '8 years',
      qualifications: 'Level 3 Volleyball Coach, Sports Science Degree',
      achievements: 'Led team to 3 national championships',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
    },
    {
      name: 'Marie Uwimana',
      sport: 'Basketball',
      experience: '5 years',
      qualifications: 'Basketball Coaching Certificate, Physical Education Degree',
      achievements: 'Developed 15+ scholarship athletes',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    },
    {
      name: 'Jean Baptiste Habimana',
      sport: 'Football',
      experience: '6 years',
      qualifications: 'FIFA Coaching License, Sports Management',
      achievements: 'Former professional player, youth development specialist',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
    },
    {
      name: 'Patrick Nkurunziza',
      sport: 'Athletics',
      experience: '10 years',
      qualifications: 'Athletics Coaching Certification, Exercise Science',
      achievements: 'Coached 5 national record holders',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face'
    }
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5)), url("https://images.unsplash.com/photo-1612872087720-bb876e2e67d1?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 30% 70%, rgba(76, 175, 80, 0.3) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(255, 152, 0, 0.3) 0%, transparent 50%)',
            animation: 'float 16s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-30px) rotate(3deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <SportsVolleyball sx={{ mr: 0.5 }} fontSize="inherit" />
              Sports at Nyanza TSS
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Sports at Nyanza TSS
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Excellence in athletics and sportsmanship. Join our championship teams and discover 
            your potential in volleyball, basketball, football, athletics, and more.
          </Typography>
        </Container>
      </Box>

      {/* Sports Statistics */}
      <Container maxWidth="lg" sx={{ py: { xs: 4, md: 6 } }}>
        <Grid container spacing={3}>
          {sportsStats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={1200 + index * 100}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${stat.color}10 0%, ${stat.color}05 100%)`,
                    borderRadius: 3,
                    border: `2px solid ${stat.color}30`,
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: `0 20px 60px ${stat.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: stat.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        boxShadow: `0 8px 25px ${stat.color}40`,
                        animation: 'pulse 2s ease-in-out infinite',
                        '@keyframes pulse': {
                          '0%, 100%': { transform: 'scale(1)' },
                          '50%': { transform: 'scale(1.05)' },
                        },
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Typography variant="h3" fontWeight={700} color={stat.color} mb={1}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="#64748b" fontWeight={500}>
                      {stat.label}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Sports Programs */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={1600}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Our Sports Programs
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Discover our diverse range of sports programs designed to develop athletic excellence and character
        </Typography>

        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
          <Tabs
            value={selectedSport}
            onChange={(e, newValue) => setSelectedSport(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem',
                minWidth: 120,
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                },
              },
              '& .Mui-selected': {
                color: '#4caf50',
              },
              '& .MuiTabs-indicator': {
                backgroundColor: '#4caf50',
                height: 3,
                borderRadius: 2,
              },
            }}
          >
            {sportsPrograms.map((sport, index) => (
              <Tab key={index} label={sport.name} />
            ))}
          </Tabs>
        </Box>

        <Grid container spacing={4}>
          {sportsPrograms.map((sport, index) => (
            <Grid item xs={12} md={6} lg={3} key={index}>
              <Grow in timeout={1800 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-12px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <Box
                    sx={{
                      height: 200,
                      backgroundImage: `linear-gradient(45deg, ${sport.color}80, ${sport.color}60), url("${sport.image}")`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Box
                      sx={{
                        width: 80,
                        height: 80,
                        bgcolor: 'rgba(255, 255, 255, 0.9)',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: sport.color,
                        fontSize: '2.5rem',
                        animation: 'float 3s ease-in-out infinite',
                        '@keyframes float': {
                          '0%, 100%': { transform: 'translateY(0px)' },
                          '50%': { transform: 'translateY(-10px)' },
                        },
                      }}
                    >
                      {sport.icon}
                    </Box>
                  </Box>

                  <CardContent sx={{ p: 4 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        color: '#1e293b',
                        mb: 2,
                        textAlign: 'center',
                      }}
                    >
                      {sport.name}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        mb: 3,
                        lineHeight: 1.6,
                        textAlign: 'center',
                      }}
                    >
                      {sport.description}
                    </Typography>

                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" color="#64748b" mb={1}>
                        <strong>Team Size:</strong> {sport.teamSize} athletes
                      </Typography>
                      <Typography variant="body2" color="#64748b" mb={1}>
                        <strong>Training:</strong> {sport.trainingDays}
                      </Typography>
                      <Typography variant="body2" color="#64748b" mb={1}>
                        <strong>Time:</strong> {sport.trainingTime}
                      </Typography>
                      <Typography variant="body2" color="#64748b">
                        <strong>Coach:</strong> {sport.coach}
                      </Typography>
                    </Box>

                    <Button
                      variant="contained"
                      fullWidth
                      sx={{
                        bgcolor: sport.color,
                        color: 'white',
                        fontWeight: 600,
                        py: 1.5,
                        borderRadius: 2,
                        textTransform: 'none',
                        '&:hover': {
                          bgcolor: sport.color,
                          filter: 'brightness(0.9)',
                        },
                      }}
                    >
                      Join Team
                    </Button>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Sports Facilities */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.05), rgba(0,0,0,0.02)), url("https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2000}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              World-Class Sports Facilities
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Train and compete in professional-grade facilities designed for athletic excellence
          </Typography>

          <Grid container spacing={4}>
            {facilities.map((facility, index) => (
              <Grid item xs={12} md={6} lg={4} key={index}>
                <Grow in timeout={2200 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      overflow: 'hidden',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <Box
                      sx={{
                        height: 200,
                        backgroundImage: `url("${facility.image}")`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        position: 'relative',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: 'linear-gradient(45deg, rgba(76, 175, 80, 0.8) 0%, rgba(255, 152, 0, 0.6) 100%)',
                        },
                      }}
                    />

                    <CardContent sx={{ p: 4 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          mb: 2,
                          color: '#1e293b',
                        }}
                      >
                        {facility.name}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          mb: 3,
                          lineHeight: 1.6,
                        }}
                      >
                        {facility.description}
                      </Typography>

                      <List dense>
                        {facility.features.map((feature, featureIndex) => (
                          <ListItem key={featureIndex} sx={{ px: 0, py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 30 }}>
                              <CheckCircle sx={{ fontSize: '1rem', color: '#4caf50' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={feature}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.85rem',
                                  color: '#64748b',
                                },
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Achievements Timeline */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2400}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Our Sporting Achievements
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          A proud history of sporting excellence and championship victories
        </Typography>

        <Timeline position="alternate">
          {achievements.map((achievement, index) => (
            <TimelineItem key={index}>
              <TimelineOppositeContent
                sx={{ m: 'auto 0' }}
                align={index % 2 === 0 ? 'right' : 'left'}
                variant="body2"
                color="#64748b"
                fontWeight={600}
              >
                {achievement.year}
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineConnector sx={{ bgcolor: achievement.color }} />
                <TimelineDot
                  sx={{
                    bgcolor: achievement.color,
                    color: 'white',
                    width: 60,
                    height: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: `0 8px 25px ${achievement.color}40`,
                    animation: `pulse 2s ease-in-out infinite ${index * 0.3}s`,
                    '@keyframes pulse': {
                      '0%, 100%': { transform: 'scale(1)' },
                      '50%': { transform: 'scale(1.1)' },
                    },
                  }}
                >
                  {achievement.icon}
                </TimelineDot>
                <TimelineConnector sx={{ bgcolor: achievement.color }} />
              </TimelineSeparator>
              <TimelineContent sx={{ py: '12px', px: 2 }}>
                <Grow in timeout={2600 + index * 200}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 3 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                        }}
                      >
                        {achievement.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          lineHeight: 1.6,
                        }}
                      >
                        {achievement.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grow>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      </Container>

      {/* Coaching Staff */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2800}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Expert Coaching Staff
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Learn from experienced coaches dedicated to developing athletic excellence and character
          </Typography>

          <Grid container spacing={4}>
            {coaches.map((coach, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Grow in timeout={3000 + index * 200}>
                  <Card
                    sx={{
                      textAlign: 'center',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      <Avatar
                        src={coach.image}
                        sx={{
                          width: 100,
                          height: 100,
                          mx: 'auto',
                          mb: 3,
                          border: '4px solid #4caf50',
                          boxShadow: '0 8px 25px rgba(76, 175, 80, 0.3)',
                        }}
                      />
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                        }}
                      >
                        {coach.name}
                      </Typography>
                      <Chip
                        label={coach.sport}
                        sx={{
                          bgcolor: '#4caf50',
                          color: 'white',
                          fontWeight: 500,
                          mb: 2,
                        }}
                      />
                      <Typography variant="body2" color="#64748b" mb={1}>
                        <strong>Experience:</strong> {coach.experience}
                      </Typography>
                      <Typography variant="body2" color="#64748b" mb={2} sx={{ fontSize: '0.8rem' }}>
                        {coach.qualifications}
                      </Typography>
                      <Typography variant="body2" color="#4caf50" fontWeight={500} sx={{ fontSize: '0.85rem' }}>
                        {coach.achievements}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6)), url("https://images.unsplash.com/photo-1612872087720-bb876e2e67d1?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={3200}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
              }}
            >
              Join Our Championship Teams
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Discover your athletic potential and be part of our winning tradition.
            Train with the best coaches and compete at the highest levels.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<SportsVolleyball />}
              sx={{
                bgcolor: 'white',
                color: '#4caf50',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Join a Team
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<Schedule />}
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              View Training Schedule
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default SportsPage
