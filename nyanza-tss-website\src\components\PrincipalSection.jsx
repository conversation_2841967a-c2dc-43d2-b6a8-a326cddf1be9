import React from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Fade,
  Grow,
  Avatar,
  Divider,
} from '@mui/material'
import {
  FormatQuote,
  School,
  EmojiEvents,
  Groups,
  TrendingUp,
  Phone,
  Email,
} from '@mui/icons-material'

const PrincipalSection = () => {
  const achievements = [
    {
      icon: <EmojiEvents />,
      title: 'National Excellence',
      description: 'Leading students to 5th & 10th place in NESA TVET 2024',
      color: '#ffd700',
    },
    {
      icon: <Groups />,
      title: 'Student Development',
      description: 'Fostering comprehensive technical skill development',
      color: '#4caf50',
    },
    {
      icon: <TrendingUp />,
      title: 'Academic Growth',
      description: 'Continuous improvement in TVET education standards',
      color: '#2196f3',
    },
  ]

  return (
    <Box
      id="principal"
      sx={{
        py: { xs: 6, md: 10 },
        background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
          `,
          zIndex: 1,
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Section Header */}
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -12,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 4,
                  background: 'linear-gradient(90deg, #ffd700, #ff9800)',
                  borderRadius: 2,
                },
              }}
            >
              Principal's Message
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'rgba(255,255,255,0.8)',
                maxWidth: '600px',
                mx: 'auto',
                fontSize: { xs: '1rem', sm: '1.1rem' },
                lineHeight: 1.6,
                mt: 4,
              }}
            >
              Leadership committed to excellence in TVET education
            </Typography>
          </Box>
        </Fade>

        <Grid container spacing={6} alignItems="center">
          {/* Principal Card */}
          <Grid item xs={12} md={5}>
            <Grow in timeout={1200}>
              <Card
                sx={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(20px)',
                  borderRadius: 4,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  overflow: 'hidden',
                  position: 'relative',
                }}
              >
                {/* Decorative Header */}
                <Box
                  sx={{
                    height: 80,
                    background: 'linear-gradient(135deg, #ffd700 0%, #ff9800 100%)',
                    position: 'relative',
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: 20,
                      background: 'rgba(255, 255, 255, 0.95)',
                      borderRadius: '50% 50% 0 0 / 100% 100% 0 0',
                    },
                  }}
                />

                <CardContent sx={{ p: 4, pt: 2, textAlign: 'center' }}>
                  {/* Principal Avatar */}
                  <Avatar
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80"
                    alt="Principal Manirambona Leonard"
                    sx={{
                      width: 120,
                      height: 120,
                      mx: 'auto',
                      mb: 3,
                      border: '4px solid white',
                      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                      mt: -8,
                    }}
                  />

                  {/* Principal Info */}
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 700,
                      color: '#1e293b',
                      mb: 1,
                      fontSize: { xs: '1.5rem', sm: '1.8rem' },
                    }}
                  >
                    Manirambona Leonard
                  </Typography>

                  <Chip
                    label="Principal"
                    sx={{
                      bgcolor: '#1e293b',
                      color: 'white',
                      fontWeight: 600,
                      mb: 2,
                      fontSize: '0.9rem',
                    }}
                  />

                  <Typography
                    variant="body1"
                    sx={{
                      color: '#64748b',
                      mb: 3,
                      fontSize: '1rem',
                      fontWeight: 500,
                    }}
                  >
                    Nyanza Technical Secondary School
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  {/* Contact Info */}
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                      <Phone sx={{ fontSize: 18, color: '#64748b' }} />
                      <Typography variant="body2" sx={{ color: '#64748b' }}>
                        +250 788 309 436
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                      <School sx={{ fontSize: 18, color: '#64748b' }} />
                      <Typography variant="body2" sx={{ color: '#64748b' }}>
                        Kigoma Sector, Nyanza District
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grow>
          </Grid>

          {/* Message Section */}
          <Grid item xs={12} md={7}>
            <Fade in timeout={1400}>
              <Box>
                {/* Quote Icon */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 4,
                  }}
                >
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      bgcolor: 'rgba(255, 215, 0, 0.2)',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 3,
                    }}
                  >
                    <FormatQuote sx={{ fontSize: '2rem', color: '#ffd700' }} />
                  </Box>
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 700,
                      color: 'white',
                      fontSize: { xs: '1.5rem', sm: '2rem' },
                    }}
                  >
                    A Message of Excellence
                  </Typography>
                </Box>

                {/* Principal's Message */}
                <Typography
                  variant="h5"
                  sx={{
                    fontStyle: 'italic',
                    mb: 4,
                    color: 'rgba(255,255,255,0.95)',
                    lineHeight: 1.7,
                    fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.4rem' },
                    fontWeight: 400,
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                  }}
                >
                  "At Nyanza Technical Secondary School, we are committed to excellence in TVET education.
                  Our students achieve national recognition in technical fields, and we continue to build
                  Rwanda's future through comprehensive technical and vocational training. Together, we
                  shape tomorrow's innovators and leaders."
                </Typography>

                {/* Signature */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    mb: 4,
                  }}
                >
                  <Box
                    sx={{
                      width: 60,
                      height: 2,
                      bgcolor: '#ffd700',
                      borderRadius: 1,
                    }}
                  />
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: 'white',
                      fontSize: '1.1rem',
                    }}
                  >
                    Manirambona Leonard
                  </Typography>
                </Box>
              </Box>
            </Fade>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default PrincipalSection
