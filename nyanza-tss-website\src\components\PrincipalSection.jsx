import React from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
} from '@mui/material'

const PrincipalSection = () => {
  return (
    <Box
      id="principal"
      sx={{
        py: 8,
        backgroundColor: 'white',
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={6} alignItems="center">
          {/* Principal Image */}
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <img
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80"
                alt="Principal"
                style={{
                  width: '100%',
                  maxWidth: '300px',
                  height: 'auto',
                  borderRadius: '8px',
                }}
              />
            </Box>
          </Grid>

          {/* Quote and Message */}
          <Grid item xs={12} md={8}>
            <Box sx={{ position: 'relative' }}>
              {/* Quote Icon */}
              <Box
                sx={{
                  position: 'absolute',
                  top: -20,
                  left: -20,
                  zIndex: 1,
                }}
              >
                <img
                  src="https://cdn-icons-png.flaticon.com/512/3135/3135715.png"
                  alt="Quote"
                  style={{
                    width: '60px',
                    height: '60px',
                    opacity: 0.3,
                  }}
                />
              </Box>

              <Typography
                variant="h5"
                sx={{
                  fontStyle: 'italic',
                  mb: 4,
                  color: '#333',
                  lineHeight: 1.6,
                  fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.5rem' },
                  position: 'relative',
                  zIndex: 2,
                }}
              >
                At Nyanza Technical Secondary School, we are committed to excellence in TVET education.
                Our students achieve national recognition in technical fields, and we continue to build
                Rwanda's future through comprehensive technical and vocational training.
              </Typography>

              <Box>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 'bold',
                    color: '#333',
                    mb: 0.5,
                  }}
                >
                  Manirambona Leonard,
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#666',
                    fontStyle: 'italic',
                  }}
                >
                  Principal, Nyanza Technical Secondary School
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default PrincipalSection
