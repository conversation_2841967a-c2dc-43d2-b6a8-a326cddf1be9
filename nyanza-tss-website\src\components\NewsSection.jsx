import React, { useState } from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  <PERSON>,
  CardContent,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ade,
  Grow,
  <PERSON>ack,
  Zoom,
  Slide,
  IconButton,
} from '@mui/material'
import {
  CalendarToday,
  TrendingUp,
  EmojiEvents,
  School,
  Groups,
  ArrowForward,
  AccessTime,
  Favorite,
  Share,
  Visibility,
  Star,
} from '@mui/icons-material'

const NewsSection = () => {
  const [hoveredCard, setHoveredCard] = useState(null)
  const newsItems = [
    {
      id: 1,
      title: 'Nyanza TSS Students Excel in National TVET Exams',
      excerpt: 'Our students achieved remarkable success in the 2024 NESA TVET examinations, securing 5th place in Industrial Electronics and 10th place in Transport & Logistics, showcasing our commitment to technical excellence.',
      image: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      date: 'December 15, 2024',
      category: 'Academic Excellence',
      icon: <EmojiEvents />,
      color: '#ffd700',
      readTime: '3 min read',
      featured: true,
    },
    {
      id: 2,
      title: 'TVET Innovation Lab Opens at Nyanza TSS',
      excerpt: 'State-of-the-art innovation laboratory equipped with modern tools and technology opens to enhance hands-on learning experience for our technical students across all 12 programs.',
      image: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      date: 'November 28, 2024',
      category: 'Infrastructure',
      icon: <School />,
      color: '#2196f3',
      readTime: '4 min read',
      featured: false,
    },
    {
      id: 3,
      title: 'Volleyball Team Wins Regional Championship',
      excerpt: 'Nyanza TSS volleyball team demonstrates exceptional teamwork and skill, securing the regional championship title and advancing to national competitions.',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      date: 'November 10, 2024',
      category: 'Sports',
      icon: <Groups />,
      color: '#4caf50',
      readTime: '2 min read',
      featured: false,
    },
    {
      id: 4,
      title: 'Industry Partnership Program Launch',
      excerpt: 'New partnerships with leading companies provide internship opportunities and real-world experience for our TVET students, bridging the gap between education and industry.',
      image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      date: 'October 22, 2024',
      category: 'Partnerships',
      icon: <TrendingUp />,
      color: '#ff9800',
      readTime: '5 min read',
      featured: false,
    },
  ]

  return (
    <Box
      id="news"
      sx={{
        py: { xs: 6, md: 10 },
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.03) 0%, transparent 50%)
          `,
          zIndex: 1,
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Section Header */}
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 3,
                color: '#1e293b',
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -12,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 4,
                  background: 'linear-gradient(90deg, #2196f3, #ff9800)',
                  borderRadius: 2,
                },
              }}
            >
              Latest News & Updates
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#64748b',
                maxWidth: '800px',
                mx: 'auto',
                fontSize: { xs: '1rem', sm: '1.2rem' },
                lineHeight: 1.6,
                mt: 4,
              }}
            >
              Stay informed about achievements, developments, and exciting events at Nyanza Technical Secondary School
            </Typography>
          </Box>
        </Fade>

        {/* Featured News */}
        {newsItems.filter(news => news.featured).map((news, index) => (
          <Slide direction="up" in timeout={1200 + index * 300} key={news.id}>
            <Card
              onMouseEnter={() => setHoveredCard(news.id)}
              onMouseLeave={() => setHoveredCard(null)}
              sx={{
                mb: 6,
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%)',
                backdropFilter: 'blur(30px)',
                borderRadius: 5,
                border: '2px solid rgba(255, 255, 255, 0.4)',
                boxShadow: hoveredCard === news.id
                  ? `0 40px 100px rgba(0, 0, 0, 0.25), 0 0 0 1px ${news.color}40`
                  : '0 25px 70px rgba(0, 0, 0, 0.12)',
                overflow: 'hidden',
                cursor: 'pointer',
                position: 'relative',
                transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                transform: hoveredCard === news.id ? 'translateY(-15px) scale(1.02)' : 'translateY(0) scale(1)',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: `linear-gradient(135deg, ${news.color}10 0%, transparent 50%)`,
                  opacity: 0.8,
                  transition: 'opacity 0.4s ease',
                  zIndex: 1,
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: -2,
                  left: -2,
                  right: -2,
                  bottom: -2,
                  background: `linear-gradient(45deg, ${news.color}40, transparent, ${news.color}40)`,
                  borderRadius: 5,
                  opacity: 0.5,
                  animation: 'borderGlow 3s ease-in-out infinite',
                  transition: 'opacity 0.4s ease',
                  zIndex: -1,
                  '@keyframes borderGlow': {
                    '0%, 100%': { opacity: 0.3 },
                    '50%': { opacity: 0.7 },
                  },
                },
              }}
            >
              <Grid container sx={{ position: 'relative', zIndex: 2 }}>
                <Grid item xs={12} md={6}>
                  <Box
                    sx={{
                      height: { xs: 280, md: 380 },
                      backgroundImage: `url(${news.image})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      overflow: 'hidden',
                      borderRadius: { xs: 0, md: '20px 0 0 20px' },
                      transform: hoveredCard === news.id ? 'scale(1.05)' : 'scale(1)',
                      transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: hoveredCard === news.id
                          ? `linear-gradient(135deg, ${news.color}20 0%, rgba(30, 41, 59, 0.2) 100%)`
                          : 'linear-gradient(135deg, rgba(30, 41, 59, 0.3) 0%, rgba(30, 41, 59, 0.1) 100%)',
                        transition: 'all 0.4s ease',
                      },
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: `radial-gradient(circle at center, transparent 0%, ${news.color}15 100%)`,
                        opacity: 0.6,
                        animation: 'glow 4s ease-in-out infinite',
                        transition: 'opacity 0.4s ease',
                        '@keyframes glow': {
                          '0%, 100%': { opacity: 0.4 },
                          '50%': { opacity: 0.8 },
                        },
                      },
                    }}
                  >
                    {/* Animated Featured Badge */}
                    <Zoom in timeout={1500}>
                      <Chip
                        label="FEATURED"
                        sx={{
                          position: 'absolute',
                          top: 20,
                          left: 20,
                          background: `linear-gradient(135deg, ${news.color} 0%, ${news.color}CC 100%)`,
                          color: 'white',
                          fontWeight: 800,
                          fontSize: '0.75rem',
                          zIndex: 3,
                          boxShadow: `0 8px 25px ${news.color}40`,
                          transform: hoveredCard === news.id ? 'scale(1.1) rotate(2deg)' : 'scale(1) rotate(0deg)',
                          transition: 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            background: `linear-gradient(135deg, ${news.color}40 0%, transparent 100%)`,
                            borderRadius: 'inherit',
                            opacity: hoveredCard === news.id ? 1 : 0,
                            transition: 'opacity 0.3s ease',
                          },
                        }}
                      />
                    </Zoom>

                    {/* Enhanced Category Badge */}
                    <Zoom in timeout={1700}>
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 20,
                          right: 20,
                          background: 'rgba(255, 255, 255, 0.95)',
                          backdropFilter: 'blur(10px)',
                          borderRadius: 4,
                          p: 1.5,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          zIndex: 3,
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                          transform: hoveredCard === news.id ? 'translateY(-5px) scale(1.05)' : 'translateY(0) scale(1)',
                          transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                          border: hoveredCard === news.id ? `2px solid ${news.color}40` : '2px solid transparent',
                        }}
                      >
                        <Box
                          sx={{
                            color: news.color,
                            fontSize: '1.1rem',
                            transform: hoveredCard === news.id ? 'rotate(360deg)' : 'rotate(0deg)',
                            transition: 'transform 0.6s ease',
                          }}
                        >
                          {news.icon}
                        </Box>
                        <Typography
                          variant="caption"
                          sx={{
                            fontWeight: 700,
                            color: '#1e293b',
                            fontSize: '0.8rem',
                          }}
                        >
                          {news.category}
                        </Typography>
                      </Box>
                    </Zoom>

                    {/* Always Visible Floating Action Buttons */}
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 20,
                        right: 20,
                        display: 'flex',
                        gap: 1,
                        opacity: 0.8,
                        transform: 'translateY(0)',
                        transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                        zIndex: 3,
                        '&:hover': {
                          opacity: 1,
                          transform: 'translateY(-3px)',
                        },
                      }}
                    >
                      <IconButton
                        size="small"
                        sx={{
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          backdropFilter: 'blur(10px)',
                          color: news.color,
                          '&:hover': {
                            bgcolor: news.color,
                            color: 'white',
                            transform: 'scale(1.1)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        <Favorite sx={{ fontSize: 16 }} />
                      </IconButton>
                      <IconButton
                        size="small"
                        sx={{
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          backdropFilter: 'blur(10px)',
                          color: news.color,
                          '&:hover': {
                            bgcolor: news.color,
                            color: 'white',
                            transform: 'scale(1.1)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        <Share sx={{ fontSize: 16 }} />
                      </IconButton>
                    </Box>

                    {/* Always Visible Sparkle Effects */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '20%',
                        left: '15%',
                        width: 4,
                        height: 4,
                        bgcolor: news.color,
                        borderRadius: '50%',
                        animation: 'sparkle 3s infinite',
                        zIndex: 3,
                        '@keyframes sparkle': {
                          '0%, 100%': { opacity: 0.3, transform: 'scale(0.8)' },
                          '50%': { opacity: 1, transform: 'scale(1.2)' },
                        },
                      }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '60%',
                        right: '25%',
                        width: 3,
                        height: 3,
                        bgcolor: news.color,
                        borderRadius: '50%',
                        animation: 'sparkle 3s infinite 1s',
                        zIndex: 3,
                        '@keyframes sparkle': {
                          '0%, 100%': { opacity: 0.3, transform: 'scale(0.8)' },
                          '50%': { opacity: 1, transform: 'scale(1.2)' },
                        },
                      }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '80%',
                        left: '70%',
                        width: 2,
                        height: 2,
                        bgcolor: news.color,
                        borderRadius: '50%',
                        animation: 'sparkle 3s infinite 2s',
                        zIndex: 3,
                        '@keyframes sparkle': {
                          '0%, 100%': { opacity: 0.3, transform: 'scale(0.8)' },
                          '50%': { opacity: 1, transform: 'scale(1.2)' },
                        },
                      }}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <CardContent
                    sx={{
                      p: { xs: 3, md: 5 },
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      zIndex: 2,
                    }}
                  >
                    {/* Animated Title */}
                    <Box sx={{ flex: 1 }}>
                      <Fade in timeout={2000}>
                        <Typography
                          variant="h4"
                          sx={{
                            fontWeight: 800,
                            mb: 3,
                            color: '#1e293b',
                            fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.2rem' },
                            lineHeight: 1.2,
                            position: 'relative',
                            transform: hoveredCard === news.id ? 'translateX(10px)' : 'translateX(0)',
                            transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                            '&::after': {
                              content: '""',
                              position: 'absolute',
                              bottom: -8,
                              left: 0,
                              width: hoveredCard === news.id ? '100%' : '0%',
                              height: 3,
                              background: `linear-gradient(90deg, ${news.color}, transparent)`,
                              transition: 'width 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                              borderRadius: 2,
                            },
                          }}
                        >
                          {news.title}
                        </Typography>
                      </Fade>

                      <Fade in timeout={2200}>
                        <Typography
                          variant="body1"
                          sx={{
                            color: '#64748b',
                            mb: 4,
                            lineHeight: 1.8,
                            fontSize: '1.1rem',
                            transform: hoveredCard === news.id ? 'translateX(5px)' : 'translateX(0)',
                            transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.1s',
                          }}
                        >
                          {news.excerpt}
                        </Typography>
                      </Fade>

                      {/* Achievement Stars for Featured News */}
                      <Fade in timeout={2400}>
                        <Box
                          sx={{
                            display: 'flex',
                            gap: 0.5,
                            mb: 3,
                            opacity: hoveredCard === news.id ? 1 : 0.7,
                            transition: 'opacity 0.3s ease',
                          }}
                        >
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              sx={{
                                fontSize: 20,
                                color: news.color,
                                transform: hoveredCard === news.id ? `scale(1.2) rotate(${i * 72}deg)` : 'scale(1) rotate(0deg)',
                                transition: `all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) ${i * 0.1}s`,
                              }}
                            />
                          ))}
                          <Typography
                            variant="body2"
                            sx={{
                              ml: 1,
                              color: news.color,
                              fontWeight: 600,
                              alignSelf: 'center',
                            }}
                          >
                            Excellence Award
                          </Typography>
                        </Box>
                      </Fade>
                    </Box>

                    {/* Enhanced Footer */}
                    <Fade in timeout={2600}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          mt: 'auto',
                          p: 2,
                          borderRadius: 3,
                          background: hoveredCard === news.id
                            ? `linear-gradient(135deg, ${news.color}08 0%, transparent 100%)`
                            : 'transparent',
                          transition: 'all 0.4s ease',
                        }}
                      >
                        <Stack direction="row" spacing={3} alignItems="center">
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              transform: hoveredCard === news.id ? 'scale(1.05)' : 'scale(1)',
                              transition: 'transform 0.3s ease',
                            }}
                          >
                            <CalendarToday sx={{ fontSize: 18, color: news.color }} />
                            <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 600 }}>
                              {news.date}
                            </Typography>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              transform: hoveredCard === news.id ? 'scale(1.05)' : 'scale(1)',
                              transition: 'transform 0.3s ease 0.1s',
                            }}
                          >
                            <AccessTime sx={{ fontSize: 18, color: news.color }} />
                            <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 600 }}>
                              {news.readTime}
                            </Typography>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              transform: hoveredCard === news.id ? 'scale(1.05)' : 'scale(1)',
                              transition: 'transform 0.3s ease 0.2s',
                            }}
                          >
                            <Visibility sx={{ fontSize: 18, color: news.color }} />
                            <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 600 }}>
                              {Math.floor(Math.random() * 500) + 100} views
                            </Typography>
                          </Box>
                        </Stack>

                        <Button
                          endIcon={<ArrowForward />}
                          sx={{
                            background: hoveredCard === news.id
                              ? `linear-gradient(135deg, ${news.color} 0%, ${news.color}CC 100%)`
                              : 'transparent',
                            color: hoveredCard === news.id ? 'white' : news.color,
                            fontWeight: 700,
                            px: 3,
                            py: 1,
                            borderRadius: 3,
                            border: `2px solid ${news.color}`,
                            '&:hover': {
                              background: `linear-gradient(135deg, ${news.color} 0%, ${news.color}CC 100%)`,
                              color: 'white',
                              transform: 'translateX(8px) scale(1.05)',
                              boxShadow: `0 8px 25px ${news.color}40`,
                            },
                            transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                          }}
                        >
                          Read More
                        </Button>
                      </Box>
                    </Fade>
                  </CardContent>
                </Grid>
              </Grid>
            </Card>
          </Slide>
        ))}

        {/* Regular News Grid */}
        <Grid container spacing={4}>
          {newsItems.filter(news => !news.featured).map((news, index) => (
            <Grid item xs={12} sm={6} md={4} key={news.id}>
              <Grow in timeout={1800 + index * 300}>
                <Card
                  onMouseEnter={() => setHoveredCard(news.id)}
                  onMouseLeave={() => setHoveredCard(null)}
                  sx={{
                    height: '100%',
                    background: hoveredCard === news.id
                      ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.92) 100%)'
                      : 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: 4,
                    border: hoveredCard === news.id
                      ? `2px solid ${news.color}60`
                      : '2px solid rgba(255, 255, 255, 0.3)',
                    cursor: 'pointer',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                    transform: hoveredCard === news.id
                      ? 'translateY(-20px) scale(1.03) rotateX(5deg)'
                      : 'translateY(0) scale(1) rotateX(0deg)',
                    boxShadow: hoveredCard === news.id
                      ? `0 30px 80px rgba(0,0,0,0.2), 0 0 0 1px ${news.color}20`
                      : '0 10px 40px rgba(0,0,0,0.1)',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: `linear-gradient(135deg, ${news.color}08 0%, transparent 50%)`,
                      opacity: 0.6,
                      transition: 'opacity 0.4s ease',
                      zIndex: 1,
                    },
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      top: -30,
                      left: -30,
                      width: 80,
                      height: 80,
                      background: `radial-gradient(circle, ${news.color}15 0%, transparent 70%)`,
                      borderRadius: '50%',
                      opacity: 0.7,
                      transform: 'scale(2)',
                      animation: 'pulse 4s ease-in-out infinite',
                      transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                      zIndex: 1,
                      '@keyframes pulse': {
                        '0%, 100%': { transform: 'scale(2)', opacity: 0.7 },
                        '50%': { transform: 'scale(2.5)', opacity: 0.4 },
                      },
                    },
                  }}
                >
                  <Box
                    sx={{
                      height: 240,
                      backgroundImage: `url(${news.image})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      overflow: 'hidden',
                      zIndex: 2,
                      transform: hoveredCard === news.id ? 'scale(1.1)' : 'scale(1)',
                      transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: hoveredCard === news.id
                          ? `linear-gradient(135deg, ${news.color}30 0%, rgba(30, 41, 59, 0.3) 100%)`
                          : 'linear-gradient(135deg, rgba(30, 41, 59, 0.4) 0%, rgba(30, 41, 59, 0.2) 100%)',
                        transition: 'all 0.4s ease',
                      },
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: `radial-gradient(circle at 70% 30%, ${news.color}15 0%, transparent 60%)`,
                        opacity: hoveredCard === news.id ? 1 : 0,
                        transition: 'opacity 0.4s ease',
                      },
                    }}
                  >
                    {/* Enhanced Category Badge */}
                    <Zoom in timeout={2000 + index * 200}>
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 16,
                          left: 16,
                          background: 'rgba(255, 255, 255, 0.95)',
                          backdropFilter: 'blur(10px)',
                          borderRadius: 3,
                          px: 2,
                          py: 1,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          zIndex: 3,
                          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                          transform: hoveredCard === news.id
                            ? 'translateY(-3px) scale(1.05)'
                            : 'translateY(0) scale(1)',
                          transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                          border: hoveredCard === news.id ? `1px solid ${news.color}40` : '1px solid transparent',
                        }}
                      >
                        <Box
                          sx={{
                            color: news.color,
                            fontSize: '1rem',
                            transform: hoveredCard === news.id ? 'rotate(360deg)' : 'rotate(0deg)',
                            transition: 'transform 0.6s ease',
                          }}
                        >
                          {news.icon}
                        </Box>
                        <Typography
                          variant="caption"
                          sx={{
                            fontWeight: 700,
                            color: '#1e293b',
                            fontSize: '0.75rem',
                          }}
                        >
                          {news.category}
                        </Typography>
                      </Box>
                    </Zoom>

                    {/* Enhanced Read Time */}
                    <Zoom in timeout={2200 + index * 200}>
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 16,
                          right: 16,
                          background: hoveredCard === news.id
                            ? `linear-gradient(135deg, ${news.color}E6 0%, ${news.color}CC 100%)`
                            : 'rgba(0, 0, 0, 0.8)',
                          backdropFilter: 'blur(10px)',
                          borderRadius: 3,
                          px: 2,
                          py: 1,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          zIndex: 3,
                          transform: hoveredCard === news.id
                            ? 'translateY(-3px) scale(1.05)'
                            : 'translateY(0) scale(1)',
                          transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                          boxShadow: hoveredCard === news.id
                            ? `0 8px 25px ${news.color}40`
                            : '0 4px 15px rgba(0, 0, 0, 0.3)',
                        }}
                      >
                        <AccessTime sx={{ fontSize: 14, color: 'white' }} />
                        <Typography
                          variant="caption"
                          sx={{
                            color: 'white',
                            fontSize: '0.75rem',
                            fontWeight: 600,
                          }}
                        >
                          {news.readTime}
                        </Typography>
                      </Box>
                    </Zoom>

                    {/* Always Visible Floating Elements */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '25%',
                        left: '20%',
                        width: 5,
                        height: 5,
                        bgcolor: 'white',
                        borderRadius: '50%',
                        animation: 'float 4s ease-in-out infinite',
                        zIndex: 3,
                        opacity: 0.7,
                        boxShadow: `0 0 10px ${news.color}40`,
                        '@keyframes float': {
                          '0%, 100%': { transform: 'translateY(0px) scale(1)' },
                          '50%': { transform: 'translateY(-8px) scale(1.1)' },
                        },
                      }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '70%',
                        right: '30%',
                        width: 4,
                        height: 4,
                        bgcolor: news.color,
                        borderRadius: '50%',
                        animation: 'float 4s ease-in-out infinite 1.5s',
                        zIndex: 3,
                        opacity: 0.8,
                        boxShadow: '0 0 8px rgba(255, 255, 255, 0.5)',
                        '@keyframes float': {
                          '0%, 100%': { transform: 'translateY(0px) scale(1)' },
                          '50%': { transform: 'translateY(-8px) scale(1.1)' },
                        },
                      }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '45%',
                        left: '75%',
                        width: 3,
                        height: 3,
                        bgcolor: 'white',
                        borderRadius: '50%',
                        animation: 'float 4s ease-in-out infinite 3s',
                        zIndex: 3,
                        opacity: 0.6,
                        boxShadow: `0 0 6px ${news.color}30`,
                        '@keyframes float': {
                          '0%, 100%': { transform: 'translateY(0px) scale(1)' },
                          '50%': { transform: 'translateY(-8px) scale(1.1)' },
                        },
                      }}
                    />
                  </Box>

                  <CardContent
                    sx={{
                      p: 3,
                      position: 'relative',
                      zIndex: 2,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    {/* Enhanced Title */}
                    <Fade in timeout={2400 + index * 200}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 800,
                          mb: 2,
                          color: '#1e293b',
                          lineHeight: 1.3,
                          fontSize: '1.15rem',
                          minHeight: '2.6em',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          position: 'relative',
                          transform: hoveredCard === news.id ? 'translateY(-2px)' : 'translateY(0)',
                          transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                          '&::after': {
                            content: '""',
                            position: 'absolute',
                            bottom: -4,
                            left: 0,
                            width: hoveredCard === news.id ? '60%' : '0%',
                            height: 2,
                            background: `linear-gradient(90deg, ${news.color}, transparent)`,
                            transition: 'width 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                            borderRadius: 1,
                          },
                        }}
                      >
                        {news.title}
                      </Typography>
                    </Fade>

                    {/* Enhanced Excerpt */}
                    <Fade in timeout={2600 + index * 200}>
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          mb: 3,
                          lineHeight: 1.7,
                          fontSize: '0.95rem',
                          minHeight: '4.8em',
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          transform: hoveredCard === news.id ? 'translateY(-1px)' : 'translateY(0)',
                          transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.1s',
                          flex: 1,
                        }}
                      >
                        {news.excerpt}
                      </Typography>
                    </Fade>

                    {/* Enhanced Footer */}
                    <Fade in timeout={2800 + index * 200}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          p: 2,
                          borderRadius: 2,
                          background: hoveredCard === news.id
                            ? `linear-gradient(135deg, ${news.color}08 0%, transparent 100%)`
                            : 'transparent',
                          transition: 'all 0.4s ease',
                          mt: 'auto',
                        }}
                      >
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              transform: hoveredCard === news.id ? 'scale(1.05)' : 'scale(1)',
                              transition: 'transform 0.3s ease',
                            }}
                          >
                            <CalendarToday sx={{ fontSize: 16, color: news.color }} />
                            <Typography
                              variant="caption"
                              sx={{
                                color: '#64748b',
                                fontSize: '0.8rem',
                                fontWeight: 600,
                              }}
                            >
                              {news.date}
                            </Typography>
                          </Box>

                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              transform: hoveredCard === news.id ? 'scale(1.05)' : 'scale(1)',
                              transition: 'transform 0.3s ease 0.1s',
                            }}
                          >
                            <Visibility sx={{ fontSize: 16, color: news.color }} />
                            <Typography
                              variant="caption"
                              sx={{
                                color: '#64748b',
                                fontSize: '0.8rem',
                                fontWeight: 600,
                              }}
                            >
                              {Math.floor(Math.random() * 200) + 50}
                            </Typography>
                          </Box>
                        </Stack>

                        <Button
                          size="small"
                          endIcon={<ArrowForward sx={{ fontSize: 16 }} />}
                          sx={{
                            background: hoveredCard === news.id
                              ? `linear-gradient(135deg, ${news.color} 0%, ${news.color}CC 100%)`
                              : 'transparent',
                            color: hoveredCard === news.id ? 'white' : news.color,
                            fontWeight: 700,
                            fontSize: '0.85rem',
                            px: 2,
                            py: 1,
                            borderRadius: 2,
                            border: `1px solid ${news.color}`,
                            minWidth: 'auto',
                            '&:hover': {
                              background: `linear-gradient(135deg, ${news.color} 0%, ${news.color}CC 100%)`,
                              color: 'white',
                              transform: 'translateX(4px) scale(1.05)',
                              boxShadow: `0 4px 15px ${news.color}40`,
                            },
                            transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                          }}
                        >
                          Read
                        </Button>
                      </Box>
                    </Fade>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  )
}

export default NewsSection
