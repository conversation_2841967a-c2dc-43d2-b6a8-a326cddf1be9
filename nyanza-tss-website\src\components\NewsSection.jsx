import React, { useState } from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  <PERSON>ton,
  Chip,
  IconButton,
  Stack,
  Fade,
  <PERSON>row,
  <PERSON>m,
} from '@mui/material'
import {
  CalendarToday,
  AccessTime,
  Visibility,
  ArrowForward,
  Favorite,
  Share,
  Star,
  EmojiEvents,
  School,
  TrendingUp,
} from '@mui/icons-material'

const NewsSection = () => {
  const [hoveredCard, setHoveredCard] = useState(null)

  const newsItems = [
    {
      id: 1,
      title: "Nyanza TSS Volleyball Team Wins National Championship",
      excerpt: "Our talented volleyball team has brought home the national championship trophy, showcasing exceptional teamwork and dedication throughout the tournament.",
      image: "https://images.unsplash.com/photo-1612872087720-bb876e2e67d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "December 15, 2024",
      readTime: "3 min read",
      category: "Sports",
      color: "#ff6b35",
      icon: <EmojiEvents />,
    },
    {
      id: 2,
      title: "Students Rank 5th Nationally in Industrial Electronics",
      excerpt: "Our Industrial Electronics students achieved remarkable success in the 2024 NESA TVET examinations, ranking 5th nationally and demonstrating excellence in technical education.",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "November 28, 2024",
      readTime: "4 min read",
      category: "Academic",
      color: "#4ecdc4",
      icon: <School />,
    },
    {
      id: 3,
      title: "Transport & Logistics Program Achieves 10th National Ranking",
      excerpt: "Our Transport & Logistics students have secured 10th position nationally in TVET examinations, highlighting the quality of our technical education programs.",
      image: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "November 20, 2024",
      readTime: "3 min read",
      category: "Achievement",
      color: "#45b7d1",
      icon: <TrendingUp />,
    },
  ]

  return (
    <Box
      id="news"
      sx={{
        py: { xs: 6, md: 10 },
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Animated Background Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.3) 0%, transparent 50%)',
          animation: 'float 20s ease-in-out infinite',
          '@keyframes float': {
            '0%, 100%': { transform: 'translateY(0px)' },
            '50%': { transform: 'translateY(-20px)' },
          },
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Section Header */}
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
              }}
            >
              Latest News & Updates
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'rgba(255, 255, 255, 0.9)',
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6,
                fontSize: { xs: '1.1rem', md: '1.3rem' },
              }}
            >
              Stay updated with the latest achievements, events, and news from Nyanza Technical Secondary School
            </Typography>
          </Box>
        </Fade>

        {/* News Cards Grid - Only 3 Cards */}
        <Grid container spacing={4}>
          {newsItems.map((news, index) => (
            <Grid item xs={12} md={4} key={news.id}>
              <Grow in timeout={1400 + index * 200}>
                <Card
                  onMouseEnter={() => setHoveredCard(news.id)}
                  onMouseLeave={() => setHoveredCard(null)}
                  sx={{
                    height: 450,
                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%)',
                    backdropFilter: 'blur(30px)',
                    borderRadius: 5,
                    border: '2px solid rgba(255, 255, 255, 0.4)',
                    boxShadow: hoveredCard === news.id
                      ? `0 40px 100px rgba(0, 0, 0, 0.25), 0 0 0 1px ${news.color}40`
                      : '0 25px 70px rgba(0, 0, 0, 0.12)',
                    overflow: 'hidden',
                    cursor: 'pointer',
                    position: 'relative',
                    transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                    transform: hoveredCard === news.id ? 'translateY(-15px) scale(1.02)' : 'translateY(0) scale(1)',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: `linear-gradient(135deg, ${news.color}10 0%, transparent 50%)`,
                      opacity: 0.8,
                      transition: 'opacity 0.4s ease',
                      zIndex: 1,
                    },
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      top: -2,
                      left: -2,
                      right: -2,
                      bottom: -2,
                      background: `linear-gradient(45deg, ${news.color}40, transparent, ${news.color}40)`,
                      borderRadius: 5,
                      opacity: 0.5,
                      animation: 'borderGlow 3s ease-in-out infinite',
                      transition: 'opacity 0.4s ease',
                      zIndex: -1,
                      '@keyframes borderGlow': {
                        '0%, 100%': { opacity: 0.3 },
                        '50%': { opacity: 0.7 },
                      },
                    },
                  }}
                >
                  {/* Image Section */}
                  <Box
                    sx={{
                      height: 250,
                      backgroundImage: `url(${news.image})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      overflow: 'hidden',
                      borderRadius: '20px 20px 0 0',
                      transform: hoveredCard === news.id ? 'scale(1.05)' : 'scale(1)',
                      transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: hoveredCard === news.id
                          ? `linear-gradient(135deg, ${news.color}20 0%, rgba(30, 41, 59, 0.2) 100%)`
                          : 'linear-gradient(135deg, rgba(30, 41, 59, 0.3) 0%, rgba(30, 41, 59, 0.1) 100%)',
                        transition: 'all 0.4s ease',
                      },
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: `radial-gradient(circle at center, transparent 0%, ${news.color}15 100%)`,
                        opacity: 0.6,
                        animation: 'glow 4s ease-in-out infinite',
                        transition: 'opacity 0.4s ease',
                        '@keyframes glow': {
                          '0%, 100%': { opacity: 0.4 },
                          '50%': { opacity: 0.8 },
                        },
                      },
                    }}
                  >
                    {/* Category Badge */}
                    <Zoom in timeout={1700}>
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 20,
                          right: 20,
                          background: 'rgba(255, 255, 255, 0.95)',
                          backdropFilter: 'blur(10px)',
                          borderRadius: 4,
                          p: 1.5,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          zIndex: 3,
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                          transform: hoveredCard === news.id ? 'translateY(-5px) scale(1.05)' : 'translateY(0) scale(1)',
                          transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                          border: hoveredCard === news.id ? `2px solid ${news.color}40` : '2px solid transparent',
                        }}
                      >
                        <Box
                          sx={{
                            color: news.color,
                            fontSize: '1.1rem',
                            transform: hoveredCard === news.id ? 'rotate(360deg)' : 'rotate(0deg)',
                            transition: 'transform 0.6s ease',
                          }}
                        >
                          {news.icon}
                        </Box>
                        <Typography
                          variant="caption"
                          sx={{
                            fontWeight: 700,
                            color: '#1e293b',
                            fontSize: '0.8rem',
                          }}
                        >
                          {news.category}
                        </Typography>
                      </Box>
                    </Zoom>

                    {/* Always Visible Floating Action Buttons */}
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 20,
                        right: 20,
                        display: 'flex',
                        gap: 1,
                        opacity: 0.8,
                        transform: 'translateY(0)',
                        transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                        zIndex: 3,
                        '&:hover': {
                          opacity: 1,
                          transform: 'translateY(-3px)',
                        },
                      }}
                    >
                      <IconButton
                        size="small"
                        sx={{
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          backdropFilter: 'blur(10px)',
                          color: news.color,
                          '&:hover': {
                            bgcolor: news.color,
                            color: 'white',
                            transform: 'scale(1.1)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        <Favorite sx={{ fontSize: 16 }} />
                      </IconButton>
                      <IconButton
                        size="small"
                        sx={{
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          backdropFilter: 'blur(10px)',
                          color: news.color,
                          '&:hover': {
                            bgcolor: news.color,
                            color: 'white',
                            transform: 'scale(1.1)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        <Share sx={{ fontSize: 16 }} />
                      </IconButton>
                    </Box>

                    {/* Always Visible Sparkle Effects */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '20%',
                        left: '15%',
                        width: 4,
                        height: 4,
                        bgcolor: news.color,
                        borderRadius: '50%',
                        animation: 'sparkle 3s infinite',
                        zIndex: 3,
                        '@keyframes sparkle': {
                          '0%, 100%': { opacity: 0.3, transform: 'scale(0.8)' },
                          '50%': { opacity: 1, transform: 'scale(1.2)' },
                        },
                      }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '60%',
                        right: '25%',
                        width: 3,
                        height: 3,
                        bgcolor: news.color,
                        borderRadius: '50%',
                        animation: 'sparkle 3s infinite 1s',
                        zIndex: 3,
                        '@keyframes sparkle': {
                          '0%, 100%': { opacity: 0.3, transform: 'scale(0.8)' },
                          '50%': { opacity: 1, transform: 'scale(1.2)' },
                        },
                      }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '80%',
                        left: '70%',
                        width: 2,
                        height: 2,
                        bgcolor: news.color,
                        borderRadius: '50%',
                        animation: 'sparkle 3s infinite 2s',
                        zIndex: 3,
                        '@keyframes sparkle': {
                          '0%, 100%': { opacity: 0.3, transform: 'scale(0.8)' },
                          '50%': { opacity: 1, transform: 'scale(1.2)' },
                        },
                      }}
                    />

                    {/* Always Visible Floating Elements */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '25%',
                        left: '20%',
                        width: 5,
                        height: 5,
                        bgcolor: 'white',
                        borderRadius: '50%',
                        animation: 'float 4s ease-in-out infinite',
                        zIndex: 3,
                        opacity: 0.7,
                        boxShadow: `0 0 10px ${news.color}40`,
                        '@keyframes float': {
                          '0%, 100%': { transform: 'translateY(0px) scale(1)' },
                          '50%': { transform: 'translateY(-8px) scale(1.1)' },
                        },
                      }}
                    />
                  </Box>

                  {/* Content Section */}
                  <CardContent
                    sx={{
                      p: 3,
                      height: 200,
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      zIndex: 2,
                    }}
                  >
                    {/* Title */}
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        mb: 2,
                        color: '#1e293b',
                        fontSize: '1.1rem',
                        lineHeight: 1.3,
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        transform: hoveredCard === news.id ? 'translateX(5px)' : 'translateX(0)',
                        transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                      }}
                    >
                      {news.title}
                    </Typography>

                    {/* Excerpt */}
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        mb: 3,
                        lineHeight: 1.6,
                        fontSize: '0.9rem',
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        flex: 1,
                      }}
                    >
                      {news.excerpt}
                    </Typography>

                    {/* Footer */}
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        mt: 'auto',
                      }}
                    >
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                          }}
                        >
                          <CalendarToday sx={{ fontSize: 14, color: news.color }} />
                          <Typography variant="caption" sx={{ color: '#64748b', fontWeight: 600 }}>
                            {news.date.split(' ')[1]} {news.date.split(' ')[2]}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                          }}
                        >
                          <AccessTime sx={{ fontSize: 14, color: news.color }} />
                          <Typography variant="caption" sx={{ color: '#64748b', fontWeight: 600 }}>
                            {news.readTime}
                          </Typography>
                        </Box>
                      </Stack>

                      <Button
                        size="small"
                        endIcon={<ArrowForward sx={{ fontSize: 14 }} />}
                        sx={{
                          color: news.color,
                          fontWeight: 600,
                          fontSize: '0.8rem',
                          '&:hover': {
                            background: `${news.color}10`,
                            transform: 'translateX(3px)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        Read
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>

        {/* View More News Button */}
        <Fade in timeout={2000}>
          <Box sx={{ textAlign: 'center', mt: 6 }}>
            <Button
              variant="contained"
              size="large"
              endIcon={<ArrowForward />}
              sx={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
                backdropFilter: 'blur(20px)',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                color: 'white',
                fontWeight: 700,
                fontSize: '1.1rem',
                px: 4,
                py: 1.5,
                borderRadius: 4,
                textTransform: 'none',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                '&:hover': {
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%)',
                  transform: 'translateY(-3px) scale(1.05)',
                  boxShadow: '0 12px 40px rgba(0, 0, 0, 0.3)',
                },
                transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
              }}
            >
              View More News
            </Button>
          </Box>
        </Fade>
      </Container>
    </Box>
  )
}

export default NewsSection
