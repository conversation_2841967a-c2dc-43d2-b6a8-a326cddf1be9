import React from 'react'
import {
  <PERSON>,
  Container,
  Typography,
  <PERSON>rid,
  Card,
  Card<PERSON>ontent,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ade,
  Grow,
  <PERSON>ack,
} from '@mui/material'
import {
  CalendarToday,
  TrendingUp,
  EmojiEvents,
  School,
  Groups,
  ArrowForward,
  AccessTime,
} from '@mui/icons-material'

const NewsSection = () => {
  const newsItems = [
    {
      id: 1,
      title: 'Nyanza TSS Students Excel in National TVET Exams',
      excerpt: 'Our students achieved remarkable success in the 2024 NESA TVET examinations, securing 5th place in Industrial Electronics and 10th place in Transport & Logistics, showcasing our commitment to technical excellence.',
      image: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      date: 'December 15, 2024',
      category: 'Academic Excellence',
      icon: <EmojiEvents />,
      color: '#ffd700',
      readTime: '3 min read',
      featured: true,
    },
    {
      id: 2,
      title: 'TVET Innovation Lab Opens at Nyanza TSS',
      excerpt: 'State-of-the-art innovation laboratory equipped with modern tools and technology opens to enhance hands-on learning experience for our technical students across all 12 programs.',
      image: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      date: 'November 28, 2024',
      category: 'Infrastructure',
      icon: <School />,
      color: '#2196f3',
      readTime: '4 min read',
      featured: false,
    },
    {
      id: 3,
      title: 'Volleyball Team Wins Regional Championship',
      excerpt: 'Nyanza TSS volleyball team demonstrates exceptional teamwork and skill, securing the regional championship title and advancing to national competitions.',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      date: 'November 10, 2024',
      category: 'Sports',
      icon: <Groups />,
      color: '#4caf50',
      readTime: '2 min read',
      featured: false,
    },
    {
      id: 4,
      title: 'Industry Partnership Program Launch',
      excerpt: 'New partnerships with leading companies provide internship opportunities and real-world experience for our TVET students, bridging the gap between education and industry.',
      image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      date: 'October 22, 2024',
      category: 'Partnerships',
      icon: <TrendingUp />,
      color: '#ff9800',
      readTime: '5 min read',
      featured: false,
    },
  ]

  return (
    <Box
      id="news"
      sx={{
        py: { xs: 6, md: 10 },
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.03) 0%, transparent 50%)
          `,
          zIndex: 1,
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Section Header */}
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 3,
                color: '#1e293b',
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -12,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 4,
                  background: 'linear-gradient(90deg, #2196f3, #ff9800)',
                  borderRadius: 2,
                },
              }}
            >
              Latest News & Updates
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#64748b',
                maxWidth: '800px',
                mx: 'auto',
                fontSize: { xs: '1rem', sm: '1.2rem' },
                lineHeight: 1.6,
                mt: 4,
              }}
            >
              Stay informed about achievements, developments, and exciting events at Nyanza Technical Secondary School
            </Typography>
          </Box>
        </Fade>

        {/* Featured News */}
        {newsItems.filter(news => news.featured).map((news) => (
          <Fade in timeout={1200} key={news.id}>
            <Card
              sx={{
                mb: 6,
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(20px)',
                borderRadius: 4,
                border: '1px solid rgba(255, 255, 255, 0.3)',
                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)',
                overflow: 'hidden',
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 30px 80px rgba(0, 0, 0, 0.15)',
                },
              }}
            >
              <Grid container>
                <Grid item xs={12} md={6}>
                  <Box
                    sx={{
                      height: { xs: 250, md: 350 },
                      backgroundImage: `url(${news.image})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.3) 0%, rgba(30, 41, 59, 0.1) 100%)',
                      },
                    }}
                  >
                    {/* Featured Badge */}
                    <Chip
                      label="FEATURED"
                      sx={{
                        position: 'absolute',
                        top: 20,
                        left: 20,
                        bgcolor: news.color,
                        color: 'white',
                        fontWeight: 700,
                        fontSize: '0.75rem',
                        zIndex: 2,
                      }}
                    />

                    {/* Category Badge */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 20,
                        right: 20,
                        bgcolor: 'rgba(255, 255, 255, 0.9)',
                        borderRadius: 3,
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        zIndex: 2,
                      }}
                    >
                      <Box sx={{ color: news.color, fontSize: '1rem' }}>
                        {news.icon}
                      </Box>
                      <Typography
                        variant="caption"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          fontSize: '0.75rem',
                        }}
                      >
                        {news.category}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <CardContent sx={{ p: { xs: 3, md: 4 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <Box sx={{ flex: 1 }}>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 700,
                          mb: 3,
                          color: '#1e293b',
                          fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2rem' },
                          lineHeight: 1.3,
                        }}
                      >
                        {news.title}
                      </Typography>

                      <Typography
                        variant="body1"
                        sx={{
                          color: '#64748b',
                          mb: 4,
                          lineHeight: 1.7,
                          fontSize: '1.1rem',
                        }}
                      >
                        {news.excerpt}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 'auto' }}>
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <CalendarToday sx={{ fontSize: 16, color: '#64748b' }} />
                          <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 500 }}>
                            {news.date}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <AccessTime sx={{ fontSize: 16, color: '#64748b' }} />
                          <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 500 }}>
                            {news.readTime}
                          </Typography>
                        </Box>
                      </Stack>

                      <Button
                        endIcon={<ArrowForward />}
                        sx={{
                          color: news.color,
                          fontWeight: 600,
                          '&:hover': {
                            bgcolor: 'transparent',
                            transform: 'translateX(4px)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        Read More
                      </Button>
                    </Box>
                  </CardContent>
                </Grid>
              </Grid>
            </Card>
          </Fade>
        ))}

        {/* Regular News Grid */}
        <Grid container spacing={4}>
          {newsItems.filter(news => !news.featured).map((news, index) => (
            <Grid item xs={12} sm={6} md={4} key={news.id}>
              <Grow in timeout={1400 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      transform: 'translateY(-12px)',
                      boxShadow: '0 20px 60px rgba(0,0,0,0.15)',
                    },
                    overflow: 'hidden',
                  }}
                >
                  <Box
                    sx={{
                      height: 220,
                      backgroundImage: `url(${news.image})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.4) 0%, rgba(30, 41, 59, 0.2) 100%)',
                      },
                    }}
                  >
                    {/* Category Badge */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 16,
                        left: 16,
                        bgcolor: 'rgba(255, 255, 255, 0.95)',
                        borderRadius: 2,
                        px: 2,
                        py: 1,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        zIndex: 2,
                      }}
                    >
                      <Box sx={{ color: news.color, fontSize: '0.9rem' }}>
                        {news.icon}
                      </Box>
                      <Typography
                        variant="caption"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          fontSize: '0.7rem',
                        }}
                      >
                        {news.category}
                      </Typography>
                    </Box>

                    {/* Read Time */}
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 16,
                        right: 16,
                        bgcolor: 'rgba(0, 0, 0, 0.7)',
                        borderRadius: 2,
                        px: 2,
                        py: 0.5,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                        zIndex: 2,
                      }}
                    >
                      <AccessTime sx={{ fontSize: 12, color: 'white' }} />
                      <Typography
                        variant="caption"
                        sx={{
                          color: 'white',
                          fontSize: '0.7rem',
                          fontWeight: 500,
                        }}
                      >
                        {news.readTime}
                      </Typography>
                    </Box>
                  </Box>

                  <CardContent sx={{ p: 3 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        mb: 2,
                        color: '#1e293b',
                        lineHeight: 1.3,
                        fontSize: '1.1rem',
                        minHeight: '2.6em',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                      }}
                    >
                      {news.title}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        mb: 3,
                        lineHeight: 1.6,
                        minHeight: '4.8em',
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                      }}
                    >
                      {news.excerpt}
                    </Typography>

                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CalendarToday sx={{ fontSize: 14, color: '#64748b' }} />
                        <Typography
                          variant="caption"
                          sx={{
                            color: '#64748b',
                            fontSize: '0.8rem',
                            fontWeight: 500,
                          }}
                        >
                          {news.date}
                        </Typography>
                      </Box>

                      <Button
                        size="small"
                        endIcon={<ArrowForward sx={{ fontSize: 14 }} />}
                        sx={{
                          color: news.color,
                          fontWeight: 600,
                          fontSize: '0.8rem',
                          minWidth: 'auto',
                          p: 0.5,
                          '&:hover': {
                            bgcolor: 'transparent',
                            transform: 'translateX(2px)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        Read
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  )
}

export default NewsSection
