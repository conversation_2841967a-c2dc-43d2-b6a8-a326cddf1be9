import React from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
} from '@mui/material'

const NewsSection = () => {
  const newsItems = [
    {
      id: 1,
      title: 'NTSS Where Creativity & learning meet',
      excerpt: 'Nurture a talent club displaying their art and craft products on 2/2/2020 during visitation day. Such products are made during the sports and clubs time. …',
      image: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      date: 'January 4, 2023',
    },
    {
      id: 2,
      title: 'Developing the next best scientists for Africa',
      excerpt: 'Scientists are an interesting breed of people, they are creative, adventurers, critical, and pragmatic. Here at NTSS, we\'re developing the next best scientists on the …',
      image: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      date: 'January 4, 2023',
    },
    {
      id: 3,
      title: 'World Scholars\' Cup Championship 2022, Nairobi',
      excerpt: 'Our team of 6 students and their coach has just returned from the world scholars\' cup competition of over 100 schools held in Nairobi, with …',
      image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      date: 'January 4, 2023',
    },
  ]

  return (
    <Box
      id="news"
      sx={{
        py: 8,
        backgroundColor: 'white',
      }}
    >
      <Container maxWidth="lg">
        {/* Section Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography
            variant="h3"
            sx={{
              fontWeight: 'bold',
              mb: 2,
              color: '#333',
              fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
            }}
          >
            Happening at NTSS
          </Typography>
          <Typography
            variant="body1"
            sx={{
              maxWidth: 800,
              mx: 'auto',
              fontSize: '1.1rem',
              color: '#666',
              lineHeight: 1.6,
            }}
          >
            Stay up-to-date with the latest news and events happening at Nyanza Technical Secondary School!
            We strive to keep you informed about all the exciting developments happening on campus,
            check back often for all the latest news from NTSS!
          </Typography>
        </Box>

        {/* News Grid */}
        <Grid container spacing={4}>
          {newsItems.map((news) => (
            <Grid item xs={12} md={4} key={news.id}>
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
                  },
                  borderRadius: 2,
                  overflow: 'hidden',
                }}
              >
                <CardMedia
                  component="img"
                  height="200"
                  image={news.image}
                  alt={news.title}
                  sx={{
                    objectFit: 'cover',
                  }}
                />
                <CardContent sx={{ p: 3 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 'bold',
                      mb: 2,
                      color: '#333',
                      lineHeight: 1.3,
                      minHeight: '3.2em',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                    }}
                  >
                    {news.title}
                  </Typography>

                  <Typography
                    variant="body2"
                    sx={{
                      color: '#666',
                      mb: 2,
                      lineHeight: 1.6,
                      minHeight: '4.8em',
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                    }}
                  >
                    {news.excerpt}
                  </Typography>

                  <Typography
                    variant="caption"
                    sx={{
                      color: '#999',
                      fontSize: '0.85rem',
                    }}
                  >
                    • {news.date}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  )
}

export default NewsSection
