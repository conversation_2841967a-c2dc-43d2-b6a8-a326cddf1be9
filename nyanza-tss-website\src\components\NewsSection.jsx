import React from 'react'
import {
  <PERSON>,
  Container,
  Typography,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>,
  Fade,
} from '@mui/material'
import {
  <PERSON>Forward,
  Star,
  EmojiEvents,
  School,
  TrendingUp,
} from '@mui/icons-material'

const NewsSection = () => {

  const newsItems = [
    {
      id: 1,
      title: "Nyanza TSS Volleyball Team Wins National Championship",
      excerpt: "Our talented volleyball team has brought home the national championship trophy, showcasing exceptional teamwork and dedication throughout the tournament.",
      image: "https://images.unsplash.com/photo-1612872087720-bb876e2e67d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "December 15, 2024",
      readTime: "3 min read",
      category: "Sports",
      color: "#ff6b35",
      icon: <EmojiEvents />,
    },
    {
      id: 2,
      title: "Students Rank 5th Nationally in Industrial Electronics",
      excerpt: "Our Industrial Electronics students achieved remarkable success in the 2024 NESA TVET examinations, ranking 5th nationally and demonstrating excellence in technical education.",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "November 28, 2024",
      readTime: "4 min read",
      category: "Academic",
      color: "#4ecdc4",
      icon: <School />,
    },
    {
      id: 3,
      title: "Transport & Logistics Program Achieves 10th National Ranking",
      excerpt: "Our Transport & Logistics students have secured 10th position nationally in TVET examinations, highlighting the quality of our technical education programs.",
      image: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      date: "November 20, 2024",
      readTime: "3 min read",
      category: "Achievement",
      color: "#45b7d1",
      icon: <TrendingUp />,
    },
  ]

  return (
    <Box
      id="news"
      sx={{
        py: { xs: 6, md: 10 },
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Animated Background Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.3) 0%, transparent 50%)',
          animation: 'float 20s ease-in-out infinite',
          '@keyframes float': {
            '0%, 100%': { transform: 'translateY(0px)' },
            '50%': { transform: 'translateY(-20px)' },
          },
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Section Header */}
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
              }}
            >
              Latest News & Updates
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'rgba(255, 255, 255, 0.9)',
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6,
                fontSize: { xs: '1.1rem', md: '1.3rem' },
              }}
            >
              Stay updated with the latest achievements, events, and news from Nyanza Technical Secondary School
            </Typography>
          </Box>
        </Fade>

        {/* News Cards Horizontal Layout - Programs Style */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            gap: { xs: 3, md: 4 },
            justifyContent: 'center',
            alignItems: 'stretch',
          }}
        >
          {newsItems.map((news, index) => (
            <Fade
              key={news.id}
              in
              timeout={800 + index * 200}
              style={{ transitionDelay: `${index * 200}ms` }}
            >
              <Card
                sx={{
                  flex: 1,
                  maxWidth: { xs: '100%', md: '350px' },
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 3,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                    '& .news-image': {
                      transform: 'scale(1.05)',
                    },
                    '& .news-icon': {
                      transform: 'scale(1.1) rotate(5deg)',
                    },
                  },
                  // Always visible background effects
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: `linear-gradient(135deg, ${news.color}08 0%, transparent 50%)`,
                    opacity: 0.6,
                    transition: 'opacity 0.4s ease',
                    zIndex: 1,
                  },
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: -2,
                    left: -2,
                    right: -2,
                    bottom: -2,
                    background: `linear-gradient(45deg, ${news.color}30, transparent, ${news.color}30)`,
                    borderRadius: 3,
                    opacity: 0.4,
                    animation: 'borderGlow 3s ease-in-out infinite',
                    transition: 'opacity 0.4s ease',
                    zIndex: -1,
                    '@keyframes borderGlow': {
                      '0%, 100%': { opacity: 0.2 },
                      '50%': { opacity: 0.6 },
                    },
                  },
                }}
              >
                {/* News Image - Programs Style */}
                <Box
                  component="img"
                  src={news.image}
                  alt={news.title}
                  className="news-image"
                  sx={{
                    width: '100%',
                    height: 140,
                    objectFit: 'cover',
                    transition: 'transform 0.3s ease',
                  }}
                />

                {/* News Icon - Programs Style */}
                <Box
                  className="news-icon"
                  sx={{
                    position: 'absolute',
                    top: 12,
                    right: 12,
                    width: 40,
                    height: 40,
                    bgcolor: news.color,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                    transition: 'all 0.3s ease',
                    fontSize: '1.2rem',
                  }}
                >
                  {news.icon}
                </Box>

                {/* Date Badge - Programs Style */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 12,
                    left: 12,
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                    color: news.color,
                    px: 1.5,
                    py: 0.5,
                    borderRadius: 2,
                    fontSize: '0.75rem',
                    fontWeight: 600,
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  }}
                >
                  {news.date.split(' ')[1]} {news.date.split(' ')[2]}
                </Box>

                {/* Category Badge */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 50,
                    left: 12,
                    bgcolor: '#fef3c7',
                    color: '#92400e',
                    px: 1,
                    py: 0.3,
                    borderRadius: 2,
                    fontSize: '0.65rem',
                    fontWeight: 600,
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                  }}
                >
                  <Star sx={{ fontSize: '0.8rem' }} />
                  {news.category}
                </Box>

                <CardContent sx={{ p: 2.5 }}>
                  {/* News Title - Programs Style */}
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      mb: 1,
                      color: '#1e293b',
                      fontSize: '1.1rem',
                      lineHeight: 1.3,
                      transition: 'color 0.3s ease',
                    }}
                  >
                    {news.title}
                  </Typography>

                  {/* News Description - Programs Style */}
                  <Typography
                    variant="body2"
                    sx={{
                      color: '#64748b',
                      fontSize: '0.9rem',
                      lineHeight: 1.5,
                      mb: 2,
                    }}
                  >
                    {news.excerpt}
                  </Typography>

                  {/* Read Time Info - Programs Style */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      pt: 1,
                      borderTop: '1px solid rgba(0,0,0,0.05)',
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color: news.color,
                        fontWeight: 600,
                        fontSize: '0.85rem',
                      }}
                    >
                      Read Time: {news.readTime}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#94a3b8',
                        fontSize: '0.8rem',
                        fontWeight: 500,
                      }}
                    >
                      {news.category}
                    </Typography>
                  </Box>
                </CardContent>
                </Card>
              </Fade>
            ))}
        </Box>

        {/* View More News Button */}
        <Fade in timeout={2000}>
          <Box sx={{ textAlign: 'center', mt: 6 }}>
            <Button
              variant="contained"
              size="large"
              endIcon={<ArrowForward />}
              sx={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
                backdropFilter: 'blur(20px)',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                color: 'white',
                fontWeight: 700,
                fontSize: '1.1rem',
                px: 4,
                py: 1.5,
                borderRadius: 4,
                textTransform: 'none',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                '&:hover': {
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%)',
                  transform: 'translateY(-3px) scale(1.05)',
                  boxShadow: '0 12px 40px rgba(0, 0, 0, 0.3)',
                },
                transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
              }}
            >
              View More News
            </Button>
          </Box>
        </Fade>
      </Container>
    </Box>
  )
}

export default NewsSection
