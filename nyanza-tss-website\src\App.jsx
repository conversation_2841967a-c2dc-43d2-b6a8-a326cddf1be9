import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Box } from '@mui/material'
import Header from './components/Header'
import HeroSection from './components/HeroSection'
import StaffSection from './components/StaffSection'
import ProgramsSection from './components/ProgramsSection'
import PrincipalSection from './components/PrincipalSection'
import NewsSection from './components/NewsSection'
import FooterRiviera from './components/FooterRiviera'
import AllProgramsPage from './pages/AllProgramsPage'
import AboutPage from './pages/AboutPage'
import PrincipalPage from './pages/PrincipalPage'
import FacilitiesPage from './pages/FacilitiesPage'
import CareersPage from './pages/CareersPage'
// Admissions Pages
import AdmissionsPage from './pages/AdmissionsPage'
import FeesPage from './pages/FeesPage'
// Academic Life Pages
import AcademicStaffPage from './pages/AcademicStaffPage'
import ComputerSciencePage from './pages/ComputerSciencePage'
import TechnicalSkillsPage from './pages/TechnicalSkillsPage'
// Student Life Pages
import DiscoverPage from './pages/DiscoverPage'
import SchoolCalendarPage from './pages/SchoolCalendarPage'
import ClubsSocietiesPage from './pages/ClubsSocietiesPage'
import SportsPage from './pages/SportsPage'
import StudentsHandbookPage from './pages/StudentsHandbookPage'
// Contacts Page
import ContactsPage from './pages/ContactsPage'

// Home Page Component
const HomePage = () => (
  <Box sx={{ minHeight: '100vh' }}>
    <HeroSection />
    <StaffSection />
    <ProgramsSection />
    <PrincipalSection />
    <NewsSection />
  </Box>
)

function App() {
  return (
    <Router>
      <Box sx={{ minHeight: '100vh' }}>
        <Header />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/programs" element={<AllProgramsPage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/principal" element={<PrincipalPage />} />
          <Route path="/facilities" element={<FacilitiesPage />} />
          <Route path="/careers" element={<CareersPage />} />
          {/* Admissions Routes */}
          <Route path="/admissions" element={<AdmissionsPage />} />
          <Route path="/fees" element={<FeesPage />} />
          {/* Academic Life Routes */}
          <Route path="/academic-staff" element={<AcademicStaffPage />} />
          <Route path="/computer-science" element={<ComputerSciencePage />} />
          <Route path="/technical-skills" element={<TechnicalSkillsPage />} />
          {/* Student Life Routes */}
          <Route path="/discover" element={<DiscoverPage />} />
          <Route path="/school-calendar" element={<SchoolCalendarPage />} />
          <Route path="/clubs-societies" element={<ClubsSocietiesPage />} />
          <Route path="/sports" element={<SportsPage />} />
          <Route path="/students-handbook" element={<StudentsHandbookPage />} />
          {/* Contacts Route */}
          <Route path="/contacts" element={<ContactsPage />} />
        </Routes>
        <FooterRiviera />
      </Box>
    </Router>
  )
}

export default App
