import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Box } from '@mui/material'
import Header from './components/Header'
import HeroSection from './components/HeroSection'
import StaffSection from './components/StaffSection'
import ProgramsSection from './components/ProgramsSection'
import WhyJoinSection from './components/WhyJoinSection'
import CurriculumSection from './components/CurriculumSection'
import PrincipalSection from './components/PrincipalSection'
import NewsSection from './components/NewsSection'
import AffiliationsSection from './components/AffiliationsSection'
import FooterRiviera from './components/FooterRiviera'
import AllProgramsPage from './pages/AllProgramsPage'

// Home Page Component
const HomePage = () => (
  <Box sx={{ minHeight: '100vh' }}>
    <HeroSection />
    <StaffSection />
    <ProgramsSection />
    <WhyJoinSection />
    <CurriculumSection />
    <PrincipalSection />
    <NewsSection />
    <AffiliationsSection />
  </Box>
)

function App() {
  return (
    <Router>
      <Box sx={{ minHeight: '100vh' }}>
        <Header />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/programs" element={<AllProgramsPage />} />
        </Routes>
        <FooterRiviera />
      </Box>
    </Router>
  )
}

export default App
