import * as React from 'react';
type StaticDateRangePickerComponent = (<TDate>(props: StaticDateRangePickerProps & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * @deprecated The StaticDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.
 * @ignore - do not document.
 */
declare const StaticDateRangePicker: StaticDateRangePickerComponent;
export default StaticDateRangePicker;
export type StaticDateRangePickerProps = Record<any, any>;