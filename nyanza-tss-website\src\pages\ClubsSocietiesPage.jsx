import React, { useState } from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  Chip,
  Button,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  Rating,
} from '@mui/material'
import {
  Home,
  Group,
  Computer,
  Science,
  Palette,
  MusicNote,
  SportsVolleyball,
  VolunteerActivism,
  Business,
  Psychology,
  ExpandMore,
  CheckCircle,
  Star,
  People,
  Event,
  EmojiEvents,
  School,
  Lightbulb,
  Camera,
  TheaterComedy,
  Build,
  Language,
} from '@mui/icons-material'

const ClubsSocietiesPage = () => {
  const [selectedCategory, setSelectedCategory] = useState(0)

  const categories = ['All Clubs', 'Technical', 'Arts & Culture', 'Sports', 'Academic', 'Community Service']

  const clubs = [
    {
      name: 'Robotics & AI Club',
      category: 'Technical',
      description: 'Building the future with robotics and artificial intelligence projects',
      members: 45,
      meetingDay: 'Wednesdays',
      meetingTime: '4:00 PM',
      achievements: ['National Robotics Competition 2nd Place', 'AI Innovation Award'],
      activities: ['Robot building workshops', 'AI programming sessions', 'Tech competitions'],
      image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=300&fit=crop',
      color: '#1976d2',
      icon: <Computer />,
      rating: 4.8,
      established: '2019'
    },
    {
      name: 'Electronics Innovation Society',
      category: 'Technical',
      description: 'Exploring cutting-edge electronics and circuit design',
      members: 38,
      meetingDay: 'Fridays',
      meetingTime: '3:30 PM',
      achievements: ['Best Innovation Project 2023', 'Electronics Expo Winner'],
      activities: ['Circuit design workshops', 'PCB manufacturing', 'Electronics projects'],
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop',
      color: '#ff9800',
      icon: <Build />,
      rating: 4.7,
      established: '2018'
    },
    {
      name: 'Drama & Theater Club',
      category: 'Arts & Culture',
      description: 'Bringing stories to life through dramatic performances',
      members: 32,
      meetingDay: 'Tuesdays',
      meetingTime: '5:00 PM',
      achievements: ['Regional Drama Festival Winner', 'Best Original Script Award'],
      activities: ['Weekly rehearsals', 'Script writing', 'Stage performances'],
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
      color: '#9c27b0',
      icon: <TheaterComedy />,
      rating: 4.9,
      established: '2017'
    },
    {
      name: 'Music & Choir Society',
      category: 'Arts & Culture',
      description: 'Harmonizing voices and creating beautiful music together',
      members: 28,
      meetingDay: 'Thursdays',
      meetingTime: '4:30 PM',
      achievements: ['National Choir Competition 3rd Place', 'Best Traditional Song Award'],
      activities: ['Choir practice', 'Music composition', 'Cultural performances'],
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop',
      color: '#e91e63',
      icon: <MusicNote />,
      rating: 4.6,
      established: '2016'
    },
    {
      name: 'Photography Club',
      category: 'Arts & Culture',
      description: 'Capturing moments and telling stories through visual art',
      members: 25,
      meetingDay: 'Saturdays',
      meetingTime: '2:00 PM',
      achievements: ['Best Student Photography Award', 'School Magazine Cover Contest'],
      activities: ['Photo walks', 'Digital editing workshops', 'Exhibition planning'],
      image: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop',
      color: '#607d8b',
      icon: <Camera />,
      rating: 4.5,
      established: '2020'
    },
    {
      name: 'Volleyball Team',
      category: 'Sports',
      description: 'Championship volleyball team representing Nyanza TSS',
      members: 18,
      meetingDay: 'Daily',
      meetingTime: '6:00 AM & 5:00 PM',
      achievements: ['National Champions 2023', 'Regional Tournament Winners'],
      activities: ['Daily training', 'Inter-school competitions', 'Team building'],
      image: 'https://images.unsplash.com/photo-1612872087720-bb876e2e67d1?w=400&h=300&fit=crop',
      color: '#4caf50',
      icon: <SportsVolleyball />,
      rating: 5.0,
      established: '2015'
    },
    {
      name: 'Debate Society',
      category: 'Academic',
      description: 'Developing critical thinking and public speaking skills',
      members: 22,
      meetingDay: 'Mondays',
      meetingTime: '4:00 PM',
      achievements: ['Inter-School Debate Champions', 'Best Speaker Award'],
      activities: ['Weekly debates', 'Public speaking training', 'Research sessions'],
      image: 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=400&h=300&fit=crop',
      color: '#3f51b5',
      icon: <Language />,
      rating: 4.7,
      established: '2018'
    },
    {
      name: 'Science Innovation Club',
      category: 'Academic',
      description: 'Exploring scientific discoveries and conducting experiments',
      members: 35,
      meetingDay: 'Wednesdays',
      meetingTime: '3:00 PM',
      achievements: ['Science Fair Grand Prize', 'Innovation Challenge Winner'],
      activities: ['Laboratory experiments', 'Science projects', 'Research presentations'],
      image: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=400&h=300&fit=crop',
      color: '#00bcd4',
      icon: <Science />,
      rating: 4.8,
      established: '2017'
    },
    {
      name: 'Community Service Club',
      category: 'Community Service',
      description: 'Making a positive impact in our local community',
      members: 42,
      meetingDay: 'Saturdays',
      meetingTime: '9:00 AM',
      achievements: ['Community Impact Award', 'Environmental Conservation Prize'],
      activities: ['Community outreach', 'Environmental projects', 'Charity events'],
      image: 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=400&h=300&fit=crop',
      color: '#8bc34a',
      icon: <VolunteerActivism />,
      rating: 4.9,
      established: '2016'
    },
    {
      name: 'Entrepreneurship Society',
      category: 'Academic',
      description: 'Fostering business skills and startup innovation',
      members: 30,
      meetingDay: 'Fridays',
      meetingTime: '4:00 PM',
      achievements: ['Best Business Plan Competition', 'Young Entrepreneur Award'],
      activities: ['Business workshops', 'Startup pitches', 'Mentorship sessions'],
      image: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400&h=300&fit=crop',
      color: '#ff5722',
      icon: <Business />,
      rating: 4.6,
      established: '2019'
    }
  ]

  const clubStats = [
    { label: 'Active Clubs', value: '15+', icon: <Group />, color: '#1976d2' },
    { label: 'Student Members', value: '350+', icon: <People />, color: '#4caf50' },
    { label: 'Weekly Events', value: '25', icon: <Event />, color: '#ff9800' },
    { label: 'Annual Awards', value: '12', icon: <EmojiEvents />, color: '#9c27b0' },
  ]

  const benefits = [
    {
      title: 'Leadership Development',
      description: 'Build leadership skills through club officer positions and project management',
      icon: <Psychology />,
      color: '#1976d2'
    },
    {
      title: 'Skill Enhancement',
      description: 'Develop technical, creative, and interpersonal skills outside the classroom',
      icon: <Lightbulb />,
      color: '#ff9800'
    },
    {
      title: 'Networking',
      description: 'Connect with like-minded peers and build lasting friendships',
      icon: <People />,
      color: '#4caf50'
    },
    {
      title: 'Achievement Recognition',
      description: 'Gain recognition for your contributions and achievements',
      icon: <EmojiEvents />,
      color: '#9c27b0'
    }
  ]

  const filteredClubs = selectedCategory === 0 
    ? clubs 
    : clubs.filter(club => club.category === categories[selectedCategory])

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5)), url("https://images.unsplash.com/photo-1529156069898-49953e39b3ac?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 25% 75%, rgba(156, 39, 176, 0.3) 0%, transparent 50%), radial-gradient(circle at 75% 25%, rgba(25, 118, 210, 0.3) 0%, transparent 50%)',
            animation: 'float 18s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-25px) rotate(2deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <Group sx={{ mr: 0.5 }} fontSize="inherit" />
              Clubs & Societies
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Clubs & Societies
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Discover your passion and develop new skills through our diverse range of clubs and societies. 
            Join a community of like-minded students and make lasting memories.
          </Typography>
        </Container>
      </Box>

      {/* Club Statistics */}
      <Container maxWidth="lg" sx={{ py: { xs: 4, md: 6 } }}>
        <Grid container spacing={3}>
          {clubStats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={1200 + index * 100}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${stat.color}10 0%, ${stat.color}05 100%)`,
                    borderRadius: 3,
                    border: `2px solid ${stat.color}30`,
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: `0 20px 60px ${stat.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: stat.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        boxShadow: `0 8px 25px ${stat.color}40`,
                        animation: 'pulse 2s ease-in-out infinite',
                        '@keyframes pulse': {
                          '0%, 100%': { transform: 'scale(1)' },
                          '50%': { transform: 'scale(1.05)' },
                        },
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Typography variant="h3" fontWeight={700} color={stat.color} mb={1}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="#64748b" fontWeight={500}>
                      {stat.label}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Club Categories Filter */}
      <Container maxWidth="lg" sx={{ py: { xs: 2, md: 4 } }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
          <Tabs
            value={selectedCategory}
            onChange={(e, newValue) => setSelectedCategory(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem',
                minWidth: 120,
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                },
              },
              '& .Mui-selected': {
                color: '#1976d2',
              },
              '& .MuiTabs-indicator': {
                backgroundColor: '#1976d2',
                height: 3,
                borderRadius: 2,
              },
            }}
          >
            {categories.map((category, index) => (
              <Tab key={index} label={category} />
            ))}
          </Tabs>
        </Box>
      </Container>

      {/* Clubs Grid */}
      <Container maxWidth="lg" sx={{ pb: { xs: 6, md: 8 } }}>
        <Grid container spacing={4}>
          {filteredClubs.map((club, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Grow in timeout={1600 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-12px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <Box
                    sx={{
                      height: 200,
                      backgroundImage: `linear-gradient(45deg, ${club.color}80, ${club.color}60), url("${club.image}")`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      display: 'flex',
                      alignItems: 'flex-end',
                      p: 2,
                    }}
                  >
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 16,
                        left: 16,
                        width: 50,
                        height: 50,
                        bgcolor: 'rgba(255, 255, 255, 0.9)',
                        borderRadius: 2,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: club.color,
                      }}
                    >
                      {club.icon}
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Chip
                        label={club.category}
                        sx={{
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          color: club.color,
                          fontWeight: 600,
                        }}
                      />
                      <Chip
                        label={`Est. ${club.established}`}
                        sx={{
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          color: '#64748b',
                          fontWeight: 500,
                        }}
                      />
                    </Box>
                  </Box>

                  <CardContent sx={{ p: 4 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                        }}
                      >
                        {club.name}
                      </Typography>
                      <Badge badgeContent={club.members} color="primary" max={999}>
                        <People sx={{ color: '#64748b' }} />
                      </Badge>
                    </Box>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        mb: 3,
                        lineHeight: 1.6,
                      }}
                    >
                      {club.description}
                    </Typography>

                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" color="#64748b" mb={1}>
                        <strong>Meeting:</strong> {club.meetingDay}s at {club.meetingTime}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Rating value={club.rating} precision={0.1} readOnly size="small" />
                        <Typography variant="body2" color="#64748b" ml={1}>
                          ({club.rating})
                        </Typography>
                      </Box>
                    </Box>

                    <Accordion sx={{ boxShadow: 'none', '&:before': { display: 'none' } }}>
                      <AccordionSummary expandIcon={<ExpandMore />}>
                        <Typography variant="body2" fontWeight={600}>
                          View Details
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Typography variant="subtitle2" fontWeight={600} mb={1}>
                          Recent Achievements:
                        </Typography>
                        <List dense>
                          {club.achievements.map((achievement, achIndex) => (
                            <ListItem key={achIndex} sx={{ px: 0, py: 0.5 }}>
                              <ListItemIcon sx={{ minWidth: 25 }}>
                                <EmojiEvents sx={{ fontSize: '1rem', color: '#ffd700' }} />
                              </ListItemIcon>
                              <ListItemText
                                primary={achievement}
                                sx={{
                                  '& .MuiListItemText-primary': {
                                    fontSize: '0.8rem',
                                    color: '#64748b',
                                  },
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </AccordionDetails>
                    </Accordion>

                    <Button
                      variant="contained"
                      fullWidth
                      sx={{
                        bgcolor: club.color,
                        color: 'white',
                        fontWeight: 600,
                        py: 1.5,
                        borderRadius: 2,
                        textTransform: 'none',
                        mt: 2,
                        '&:hover': {
                          bgcolor: club.color,
                          filter: 'brightness(0.9)',
                        },
                      }}
                    >
                      Join Club
                    </Button>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Benefits of Joining */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.05), rgba(0,0,0,0.02)), url("https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2000}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Why Join Our Clubs?
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Discover the amazing benefits of being part of our vibrant club community
          </Typography>

          <Grid container spacing={4}>
            {benefits.map((benefit, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Grow in timeout={2200 + index * 200}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Box
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: benefit.color,
                            borderRadius: 3,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            mr: 3,
                            boxShadow: `0 8px 25px ${benefit.color}40`,
                          }}
                        >
                          {benefit.icon}
                        </Box>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 600,
                            color: '#1e293b',
                          }}
                        >
                          {benefit.title}
                        </Typography>
                      </Box>
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          lineHeight: 1.6,
                        }}
                      >
                        {benefit.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* How to Join */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2400}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            How to Join a Club
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Getting involved is easy! Follow these simple steps to join any club or society
        </Typography>

        <Grid container spacing={4} sx={{ maxWidth: 800, mx: 'auto' }}>
          {[
            {
              step: '1',
              title: 'Explore Clubs',
              description: 'Browse through our diverse range of clubs and find ones that match your interests',
              color: '#1976d2'
            },
            {
              step: '2',
              title: 'Attend a Meeting',
              description: 'Visit a club meeting to get a feel for the activities and meet current members',
              color: '#4caf50'
            },
            {
              step: '3',
              title: 'Complete Registration',
              description: 'Fill out the membership form and pay any required dues (most clubs are free)',
              color: '#ff9800'
            },
            {
              step: '4',
              title: 'Start Participating',
              description: 'Begin attending regular meetings and participating in club activities and events',
              color: '#9c27b0'
            }
          ].map((step, index) => (
            <Grid item xs={12} sm={6} key={index}>
              <Grow in timeout={2600 + index * 200}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box
                      sx={{
                        width: 80,
                        height: 80,
                        bgcolor: step.color,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 3,
                        boxShadow: `0 8px 25px ${step.color}40`,
                        fontSize: '2rem',
                        fontWeight: 700,
                      }}
                    >
                      {step.step}
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        color: '#1e293b',
                        mb: 2,
                      }}
                    >
                      {step.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                      }}
                    >
                      {step.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6)), url("https://images.unsplash.com/photo-1529156069898-49953e39b3ac?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={2800}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
              }}
            >
              Ready to Get Involved?
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Join one of our amazing clubs and societies today. Make new friends, develop skills,
            and create memories that will last a lifetime.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<Group />}
              sx={{
                bgcolor: 'white',
                color: '#1976d2',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Join a Club Today
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<Event />}
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              View Events Calendar
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default ClubsSocietiesPage
