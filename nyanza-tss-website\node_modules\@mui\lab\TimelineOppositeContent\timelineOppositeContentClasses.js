"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.getTimelineOppositeContentUtilityClass = getTimelineOppositeContentUtilityClass;
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
function getTimelineOppositeContentUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiTimelineOppositeContent', slot);
}
const timelineOppositeContentClasses = (0, _generateUtilityClasses.default)('MuiTimelineOppositeContent', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);
var _default = exports.default = timelineOppositeContentClasses;