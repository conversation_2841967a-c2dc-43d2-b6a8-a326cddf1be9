import React from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  Button,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from '@mui/material'
import {
  Home,
  School,
  CheckCircle,
  Assignment,
  PersonAdd,
  Payment,
  Schedule,
  Description,
  Star,
  TrendingUp,
  EmojiEvents,
  Groups,
} from '@mui/icons-material'

const AdmissionsPage = () => {
  const admissionSteps = [
    {
      label: 'Application Submission',
      description: 'Complete and submit your application form with required documents',
      icon: <Assignment />,
      color: '#1976d2',
      details: [
        'Fill out the online application form',
        'Submit academic transcripts',
        'Provide recommendation letters',
        'Pay application fee (5,000 RWF)'
      ]
    },
    {
      label: 'Document Verification',
      description: 'Our admissions team reviews your submitted documents',
      icon: <Description />,
      color: '#2e7d32',
      details: [
        'Academic records verification',
        'Identity document check',
        'Previous school certificates',
        'Medical certificate review'
      ]
    },
    {
      label: 'Entrance Assessment',
      description: 'Take our technical aptitude and academic assessment',
      icon: <School />,
      color: '#ed6c02',
      details: [
        'Mathematics assessment',
        'Technical aptitude test',
        'English proficiency test',
        'Interview with faculty'
      ]
    },
    {
      label: 'Admission Decision',
      description: 'Receive your admission decision and enrollment instructions',
      icon: <CheckCircle />,
      color: '#9c27b0',
      details: [
        'Admission notification via email',
        'Program placement confirmation',
        'Enrollment package delivery',
        'Orientation schedule provided'
      ]
    },
  ]

  const requirements = [
    {
      title: 'Academic Requirements',
      items: [
        'Completed O-Level (Senior 3) with minimum aggregate of 20 points',
        'Strong performance in Mathematics and Science subjects',
        'English proficiency certificate',
        'School leaving certificate from previous institution'
      ],
      icon: <School />,
      color: '#1976d2'
    },
    {
      title: 'Documentation',
      items: [
        'Completed application form',
        'Birth certificate or national ID',
        'Academic transcripts and certificates',
        'Two passport-size photographs',
        'Medical certificate from approved health center'
      ],
      icon: <Description />,
      color: '#2e7d32'
    },
    {
      title: 'Financial Requirements',
      items: [
        'Application fee: 5,000 RWF',
        'First semester fees payment',
        'Proof of financial capability',
        'Scholarship application (if applicable)'
      ],
      icon: <Payment />,
      color: '#ed6c02'
    },
  ]

  const importantDates = [
    {
      date: 'January 15',
      event: 'Application Period Opens',
      description: 'Online applications become available for the new academic year',
      color: '#1976d2'
    },
    {
      date: 'March 30',
      event: 'Application Deadline',
      description: 'Final date for submitting complete applications',
      color: '#d32f2f'
    },
    {
      date: 'April 15',
      event: 'Entrance Assessments',
      description: 'Technical aptitude and academic assessments conducted',
      color: '#ed6c02'
    },
    {
      date: 'May 10',
      event: 'Admission Results',
      description: 'Admission decisions communicated to all applicants',
      color: '#2e7d32'
    },
    {
      date: 'June 1',
      event: 'Enrollment Period',
      description: 'Accepted students complete enrollment and fee payment',
      color: '#9c27b0'
    },
    {
      date: 'July 15',
      event: 'Orientation Week',
      description: 'New student orientation and program introduction',
      color: '#795548'
    },
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Background */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.3) 0%, transparent 50%)',
            animation: 'float 20s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-20px) rotate(2deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <PersonAdd sx={{ mr: 0.5 }} fontSize="inherit" />
              Admissions
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Join Nyanza TSS
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Start your journey towards technical excellence. Discover how to apply for our 
            world-class TVET programs and become part of Rwanda's next generation of skilled professionals.
          </Typography>
        </Container>
      </Box>

      {/* Admission Process Steps */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={1200}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Admission Process
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Follow these simple steps to join our community of future technical leaders
        </Typography>

        <Grid container spacing={4}>
          {admissionSteps.map((step, index) => (
            <Grid item xs={12} md={6} lg={3} key={index}>
              <Grow in timeout={1400 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-12px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 4,
                      background: `linear-gradient(90deg, ${step.color} 0%, ${step.color}80 100%)`,
                      animation: 'shimmer 2s ease-in-out infinite',
                      '@keyframes shimmer': {
                        '0%, 100%': { opacity: 0.8 },
                        '50%': { opacity: 1 },
                      },
                    },
                  }}
                >
                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    {/* Step Number */}
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: step.color,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        fontSize: '1.5rem',
                        fontWeight: 700,
                        boxShadow: `0 8px 25px ${step.color}40`,
                        animation: 'pulse 2s ease-in-out infinite',
                        '@keyframes pulse': {
                          '0%, 100%': { transform: 'scale(1)' },
                          '50%': { transform: 'scale(1.05)' },
                        },
                      }}
                    >
                      {index + 1}
                    </Box>

                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        color: '#1e293b',
                      }}
                    >
                      {step.label}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                        mb: 3,
                      }}
                    >
                      {step.description}
                    </Typography>

                    <List dense>
                      {step.details.map((detail, detailIndex) => (
                        <ListItem key={detailIndex} sx={{ px: 0, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 30 }}>
                            <CheckCircle sx={{ fontSize: '1rem', color: step.color }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={detail}
                            sx={{
                              '& .MuiListItemText-primary': {
                                fontSize: '0.85rem',
                                color: '#64748b',
                              },
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Requirements Section */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={1600}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Admission Requirements
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Ensure you meet all requirements before submitting your application
          </Typography>

          <Grid container spacing={4}>
            {requirements.map((requirement, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Grow in timeout={1800 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      <Box
                        sx={{
                          width: 70,
                          height: 70,
                          bgcolor: requirement.color,
                          borderRadius: 3,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          mb: 3,
                          boxShadow: `0 8px 25px ${requirement.color}40`,
                        }}
                      >
                        {requirement.icon}
                      </Box>

                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          mb: 3,
                          color: '#1e293b',
                        }}
                      >
                        {requirement.title}
                      </Typography>

                      <List>
                        {requirement.items.map((item, itemIndex) => (
                          <ListItem key={itemIndex} sx={{ px: 0, py: 1 }}>
                            <ListItemIcon sx={{ minWidth: 35 }}>
                              <CheckCircle sx={{ fontSize: '1.2rem', color: requirement.color }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={item}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.95rem',
                                  color: '#64748b',
                                  lineHeight: 1.5,
                                },
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Important Dates Timeline */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2000}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Important Dates
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Mark your calendar with these key admission dates for the 2024-2025 academic year
        </Typography>

        <Timeline position="alternate">
          {importantDates.map((date, index) => (
            <TimelineItem key={index}>
              <TimelineOppositeContent
                sx={{ m: 'auto 0' }}
                align={index % 2 === 0 ? 'right' : 'left'}
                variant="body2"
                color="text.secondary"
              >
                <Chip
                  label={date.date}
                  sx={{
                    bgcolor: date.color,
                    color: 'white',
                    fontWeight: 600,
                    fontSize: '0.9rem',
                    animation: `fadeInScale 1s ease-out ${2200 + index * 200}ms both`,
                    '@keyframes fadeInScale': {
                      '0%': { opacity: 0, transform: 'scale(0.8)' },
                      '100%': { opacity: 1, transform: 'scale(1)' },
                    },
                  }}
                />
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineDot
                  sx={{
                    bgcolor: date.color,
                    boxShadow: `0 0 20px ${date.color}60`,
                    animation: `pulse 2s ease-in-out infinite ${index * 0.5}s`,
                    '@keyframes pulse': {
                      '0%, 100%': { transform: 'scale(1)' },
                      '50%': { transform: 'scale(1.1)' },
                    },
                  }}
                >
                  <Schedule sx={{ color: 'white' }} />
                </TimelineDot>
                {index < importantDates.length - 1 && <TimelineConnector />}
              </TimelineSeparator>
              <TimelineContent sx={{ py: '12px', px: 2 }}>
                <Grow in timeout={2200 + index * 200}>
                  <Card
                    sx={{
                      p: 3,
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 2,
                      border: `2px solid ${date.color}20`,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 8px 30px ${date.color}30`,
                      },
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        color: date.color,
                        mb: 1,
                      }}
                    >
                      {date.event}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.5,
                      }}
                    >
                      {date.description}
                    </Typography>
                  </Card>
                </Grow>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      </Container>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={2400}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Ready to Apply?
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Take the first step towards your technical career. Submit your application today
            and join Rwanda's premier technical education institution.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                bgcolor: 'white',
                color: '#1976d2',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Start Application
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              Download Brochure
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default AdmissionsPage
