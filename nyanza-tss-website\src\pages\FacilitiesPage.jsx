import React from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  CardMedia,
} from '@mui/material'
import {
  Home,
  School,
  Computer,
  Build,
  Science,
  SportsVolleyball,
  MenuBook,
  Restaurant,
  Hotel,
  LocalHospital,
  DirectionsCar,
  Engineering,
} from '@mui/icons-material'

const FacilitiesPage = () => {
  const facilities = [
    {
      title: "Computer Science Laboratory",
      description: "State-of-the-art computer lab with modern PCs, high-speed internet, and specialized software for programming, design, and technical applications.",
      image: "https://images.unsplash.com/photo-1562774053-701939374585?w=400&h=250&fit=crop",
      icon: <Computer />,
      color: "#2196f3",
      features: ["40+ Modern Computers", "High-Speed Internet", "Programming Software", "Design Applications"]
    },
    {
      title: "Electronics Workshop",
      description: "Fully equipped electronics laboratory with soldering stations, oscilloscopes, multimeters, and components for hands-on electronics training.",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop",
      icon: <Engineering />,
      color: "#ff9800",
      features: ["Soldering Stations", "Testing Equipment", "Electronic Components", "Circuit Design Tools"]
    },
    {
      title: "Automotive Workshop",
      description: "Professional automotive workshop with vehicle lifts, diagnostic equipment, and tools for comprehensive automotive training programs.",
      image: "https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=400&h=250&fit=crop",
      icon: <DirectionsCar />,
      color: "#4caf50",
      features: ["Vehicle Lifts", "Diagnostic Tools", "Engine Components", "Safety Equipment"]
    },
    {
      title: "Construction Workshop",
      description: "Spacious workshop for masonry, carpentry, and construction training with professional tools and safety equipment.",
      image: "https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=400&h=250&fit=crop",
      icon: <Build />,
      color: "#795548",
      features: ["Carpentry Tools", "Masonry Equipment", "Safety Gear", "Construction Materials"]
    },
    {
      title: "Science Laboratory",
      description: "Well-equipped science laboratory for physics, chemistry, and applied sciences supporting our technical programs.",
      image: "https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=400&h=250&fit=crop",
      icon: <Science />,
      color: "#9c27b0",
      features: ["Lab Equipment", "Chemical Supplies", "Safety Systems", "Experimental Setup"]
    },
    {
      title: "Sports Complex",
      description: "Multi-purpose sports facilities including volleyball courts where our championship team trains and competes.",
      image: "https://images.unsplash.com/photo-1612872087720-bb876e2e67d1?w=400&h=250&fit=crop",
      icon: <SportsVolleyball />,
      color: "#ff5722",
      features: ["Volleyball Courts", "Training Equipment", "Changing Rooms", "Spectator Areas"]
    },
    {
      title: "Library & Resource Center",
      description: "Modern library with technical books, digital resources, and quiet study areas for research and learning.",
      image: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=250&fit=crop",
      icon: <MenuBook />,
      color: "#607d8b",
      features: ["Technical Books", "Digital Resources", "Study Areas", "Research Facilities"]
    },
    {
      title: "Student Cafeteria",
      description: "Clean and modern cafeteria serving nutritious meals to support student health and well-being throughout the day.",
      image: "https://images.unsplash.com/photo-1567521464027-f127ff144326?w=400&h=250&fit=crop",
      icon: <Restaurant />,
      color: "#ff6b35",
      features: ["Nutritious Meals", "Clean Environment", "Seating Areas", "Healthy Options"]
    },
    {
      title: "Student Dormitories",
      description: "Comfortable and secure dormitory facilities providing a safe living environment for students from distant areas.",
      image: "https://images.unsplash.com/photo-**********-bab0e564b8d5?w=400&h=250&fit=crop",
      icon: <Hotel />,
      color: "#3f51b5",
      features: ["Secure Accommodation", "Study Areas", "Common Rooms", "24/7 Security"]
    },
    {
      title: "Health Center",
      description: "On-campus health facility providing basic medical care and health services for students and staff.",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=250&fit=crop",
      icon: <LocalHospital />,
      color: "#e91e63",
      features: ["Medical Care", "First Aid", "Health Monitoring", "Emergency Response"]
    },
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.3) 0%, transparent 50%)',
            animation: 'float 20s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px)' },
              '50%': { transform: 'translateY(-20px)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <School sx={{ mr: 0.5 }} fontSize="inherit" />
              Facilities
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
              }}
            >
              Our Facilities
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
            }}
          >
            Discover our modern facilities designed to provide the best learning environment for technical 
            and vocational education. From state-of-the-art laboratories to comfortable living spaces.
          </Typography>
        </Container>
      </Box>

      {/* Facilities Grid */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={1200}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            World-Class Learning Environment
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Our comprehensive facilities support hands-on learning and provide students with the tools 
          they need to excel in their chosen technical fields.
        </Typography>

        <Grid container spacing={4}>
          {facilities.map((facility, index) => (
            <Grid item xs={12} sm={6} lg={4} key={index}>
              <Grow in timeout={1400 + index * 100}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                      '& .facility-image': {
                        transform: 'scale(1.05)',
                      },
                      '& .facility-icon': {
                        transform: 'scale(1.1) rotate(5deg)',
                      },
                    },
                  }}
                >
                  {/* Facility Image */}
                  <Box sx={{ position: 'relative', overflow: 'hidden' }}>
                    <CardMedia
                      component="img"
                      height="200"
                      image={facility.image}
                      alt={facility.title}
                      className="facility-image"
                      sx={{
                        transition: 'transform 0.3s ease',
                      }}
                    />
                    
                    {/* Icon Overlay */}
                    <Box
                      className="facility-icon"
                      sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        width: 50,
                        height: 50,
                        bgcolor: facility.color,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '1.5rem',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                        transition: 'transform 0.3s ease',
                      }}
                    >
                      {facility.icon}
                    </Box>
                  </Box>

                  <CardContent sx={{ p: 3 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        color: '#1e293b',
                        fontSize: '1.2rem',
                      }}
                    >
                      {facility.title}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                        mb: 3,
                      }}
                    >
                      {facility.description}
                    </Typography>

                    {/* Features List */}
                    <Box sx={{ mt: 2 }}>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                        }}
                      >
                        Key Features:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {facility.features.map((feature, featureIndex) => (
                          <Box
                            key={featureIndex}
                            sx={{
                              px: 2,
                              py: 0.5,
                              bgcolor: `${facility.color}15`,
                              color: facility.color,
                              borderRadius: 2,
                              fontSize: '0.75rem',
                              fontWeight: 500,
                              border: `1px solid ${facility.color}30`,
                            }}
                          >
                            {feature}
                          </Box>
                        ))}
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center' }}>
            <Fade in timeout={2000}>
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 700,
                  mb: 3,
                  color: '#1e293b',
                  fontSize: { xs: '2rem', md: '2.5rem' },
                }}
              >
                Experience Our Facilities
              </Typography>
            </Fade>

            <Typography
              variant="body1"
              sx={{
                color: '#64748b',
                fontSize: '1.1rem',
                lineHeight: 1.8,
                maxWidth: '600px',
                mx: 'auto',
                mb: 4,
              }}
            >
              Visit Nyanza Technical Secondary School to see our world-class facilities firsthand. 
              Our doors are open for prospective students and their families to explore what makes 
              our technical education programs exceptional.
            </Typography>

            <Typography
              variant="body1"
              sx={{
                color: '#1976d2',
                fontSize: '1.1rem',
                fontWeight: 600,
              }}
            >
              Contact us at 0788309436 to schedule a campus tour
            </Typography>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default FacilitiesPage
