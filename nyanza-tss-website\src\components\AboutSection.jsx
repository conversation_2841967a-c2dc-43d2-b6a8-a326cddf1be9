import React from 'react'
import {
  <PERSON>,
  Container,
  Typography,
  Grid,
  <PERSON>,
  Card<PERSON><PERSON>nt,
  <PERSON><PERSON>,
  Chip,
  Fade,
  Grow,
} from '@mui/material'
import {
  EmojiEvents,
  School,
  LocationOn,
  Groups,
  TrendingUp,
  Engineering,
} from '@mui/icons-material'

const AboutSection = () => {
  const quickLinks = [
    {
      title: 'TVET Programs',
      image: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      href: '#programs',
      icon: <Engineering />,
      description: 'Level 3-5 Technical Programs'
    },
    {
      title: 'Admissions',
      image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      href: '#admissions',
      icon: <School />,
      description: 'Join Our Excellence'
    },
    {
      title: 'Our Location',
      image: 'https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      href: '#contacts',
      icon: <LocationOn />,
      description: 'Kigoma Sector, Nyanza'
    },
    {
      title: 'Student Life',
      image: 'https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      href: '#sports',
      icon: <Groups />,
      description: 'Sports & Activities'
    },
  ]

  const achievements = [
    {
      title: '5th Place',
      subtitle: 'Industrial Electronics',
      description: '2024 NESA TVET National Exams',
      color: '#ffd700',
      icon: <EmojiEvents />
    },
    {
      title: '10th Place',
      subtitle: 'Transport & Logistics',
      description: '2024 NESA TVET National Exams',
      color: '#4caf50',
      icon: <TrendingUp />
    },
  ]

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <Box
      id="about"
      sx={{
        py: { xs: 6, md: 10 },
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.03) 0%, transparent 50%)
          `,
          zIndex: 1,
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Section Header */}
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 3,
                color: '#1e293b',
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -12,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 4,
                  background: 'linear-gradient(90deg, #2196f3, #ff9800)',
                  borderRadius: 2,
                },
              }}
            >
              About Nyanza TSS
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#64748b',
                maxWidth: '800px',
                mx: 'auto',
                fontSize: { xs: '1rem', sm: '1.2rem' },
                lineHeight: 1.6,
                mt: 4,
              }}
            >
              Excellence in TVET education with proven national recognition, located in Kigoma Sector, Nyanza District
            </Typography>
          </Box>
        </Fade>

        {/* Achievement Highlights */}
        <Box sx={{ mb: { xs: 6, md: 8 } }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 4,
              textAlign: 'center',
              color: '#1e293b',
              fontSize: { xs: '1.5rem', sm: '2rem' },
            }}
          >
            🏆 Recent Achievements
          </Typography>
          <Grid container spacing={3} justifyContent="center">
            {achievements.map((achievement, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Grow in timeout={800 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.9)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      textAlign: 'center',
                      p: 3,
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: achievement.color,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        fontSize: '1.8rem',
                      }}
                    >
                      {achievement.icon}
                    </Box>
                    <Typography
                      variant="h4"
                      sx={{
                        fontWeight: 800,
                        color: achievement.color,
                        mb: 1,
                      }}
                    >
                      {achievement.title}
                    </Typography>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        color: '#1e293b',
                        mb: 1,
                      }}
                    >
                      {achievement.subtitle}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        fontSize: '0.9rem',
                      }}
                    >
                      {achievement.description}
                    </Typography>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Main Description Section */}
        <Grid container spacing={6} alignItems="center" sx={{ mb: 8 }}>
          <Grid item xs={12} md={6}>
            <Fade in timeout={1200}>
              <Box>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 700,
                    mb: 3,
                    color: '#1e293b',
                    fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.5rem' },
                    lineHeight: 1.3,
                  }}
                >
                  Learner-Centered Excellence in TVET Education
                </Typography>

                <Typography
                  variant="body1"
                  sx={{
                    mb: 4,
                    lineHeight: 1.8,
                    fontSize: '1.1rem',
                    color: '#64748b',
                  }}
                >
                  Nyanza Technical Secondary School delivers learner-centered education underpinned by
                  established teaching skills and a strong school culture. We ensure high-performing,
                  disciplined, ethical, and self-developmental learning environments that prepare
                  students for Rwanda's technological future.
                </Typography>

                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 4 }}>
                  <Chip
                    label="Level 3-5 TVET"
                    sx={{
                      bgcolor: '#e3f2fd',
                      color: '#1976d2',
                      fontWeight: 600,
                    }}
                  />
                  <Chip
                    label="12+ Programs"
                    sx={{
                      bgcolor: '#f3e5f5',
                      color: '#7b1fa2',
                      fontWeight: 600,
                    }}
                  />
                  <Chip
                    label="National Recognition"
                    sx={{
                      bgcolor: '#fff3e0',
                      color: '#f57c00',
                      fontWeight: 600,
                    }}
                  />
                </Box>

                <Button
                  variant="contained"
                  size="large"
                  onClick={() => scrollToSection('#programs')}
                  sx={{
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                    color: 'white',
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    borderRadius: 3,
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 8px 25px rgba(25, 118, 210, 0.3)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  Explore Our Programs
                </Button>
              </Box>
            </Fade>
          </Grid>

          <Grid item xs={12} md={6}>
            <Fade in timeout={1400}>
              <Box
                sx={{
                  position: 'relative',
                  borderRadius: 4,
                  overflow: 'hidden',
                  boxShadow: '0 20px 60px rgba(0,0,0,0.1)',
                }}
              >
                <img
                  src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                  alt="TVET Education at Nyanza TSS"
                  style={{
                    width: '100%',
                    height: '450px',
                    objectFit: 'cover',
                  }}
                />
                {/* Overlay with school info */}
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
                    color: 'white',
                    p: 3,
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 1 }}>
                    Nyanza Technical Secondary School
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Kigoma Sector, Butansinda Cell, Nyanza District, Southern Province
                  </Typography>
                </Box>
              </Box>
            </Fade>
          </Grid>
        </Grid>

        {/* Quick Links Grid */}
        <Box sx={{ mb: 6 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 4,
              textAlign: 'center',
              color: '#1e293b',
              fontSize: { xs: '1.5rem', sm: '2rem' },
            }}
          >
            Explore Our School
          </Typography>
          <Grid container spacing={4}>
            {quickLinks.map((link, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Grow in timeout={1000 + index * 200}>
                  <Card
                    onClick={() => scrollToSection(link.href)}
                    sx={{
                      cursor: 'pointer',
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.9)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        transform: 'translateY(-12px)',
                        boxShadow: '0 20px 60px rgba(0,0,0,0.15)',
                      },
                      overflow: 'hidden',
                    }}
                  >
                    <Box
                      sx={{
                        height: 180,
                        backgroundImage: `url(${link.image})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        position: 'relative',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.7) 0%, rgba(30, 41, 59, 0.4) 100%)',
                          transition: 'all 0.3s ease',
                        },
                        '&:hover::before': {
                          background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(30, 41, 59, 0.5) 100%)',
                        }
                      }}
                    >
                      {/* Icon */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 20,
                          right: 20,
                          width: 40,
                          height: 40,
                          bgcolor: 'rgba(255, 255, 255, 0.2)',
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontSize: '1.2rem',
                          zIndex: 2,
                        }}
                      >
                        {link.icon}
                      </Box>

                      {/* Content */}
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          p: 3,
                          zIndex: 2,
                        }}
                      >
                        <Typography
                          variant="h6"
                          sx={{
                            color: 'white',
                            fontWeight: 700,
                            mb: 1,
                            fontSize: '1.1rem',
                          }}
                        >
                          {link.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '0.85rem',
                          }}
                        >
                          {link.description}
                        </Typography>
                      </Box>
                    </Box>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Container>
    </Box>
  )
}

export default AboutSection
