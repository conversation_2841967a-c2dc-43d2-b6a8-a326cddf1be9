import React from 'react'
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  But<PERSON>,
} from '@mui/material'

const AboutSection = () => {
  const quickLinks = [
    {
      title: 'University acceptances',
      image: 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png',
      href: '#university'
    },
    {
      title: 'Admissions',
      image: 'https://cdn-icons-png.flaticon.com/512/3135/3135789.png',
      href: '#admissions'
    },
    {
      title: 'discover NTSS',
      image: 'https://cdn-icons-png.flaticon.com/512/3135/3135768.png',
      href: '#discover'
    },
    {
      title: 'School Calendar',
      image: 'https://cdn-icons-png.flaticon.com/512/3135/3135823.png',
      href: '#calendar'
    },
  ]

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <Box
      id="about"
      sx={{
        py: 8,
        backgroundColor: '#f8f9fa',
      }}
    >
      <Container maxWidth="lg">
        {/* Main Description Section */}
        <Grid container spacing={6} alignItems="center" sx={{ mb: 8 }}>
          <Grid item xs={12} md={6}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 'bold',
                mb: 3,
                color: '#333',
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                lineHeight: 1.2,
              }}
            >
              Nyanza Technical Secondary School delivers a learner centred education
            </Typography>

            <Typography
              variant="body1"
              sx={{
                mb: 4,
                lineHeight: 1.8,
                fontSize: '1.1rem',
                color: '#666',
              }}
            >
              Nyanza Technical Secondary School delivers a learner centred education underpinned by
              established teaching skills and a strong school culture which ensures that it is
              high-performing, disciplined, ethical, self-developmental and aware of its educational
              and social environment.
            </Typography>

            <Button
              variant="contained"
              size="large"
              onClick={() => scrollToSection('#discover')}
              sx={{
                backgroundColor: '#1976d2',
                color: 'white',
                px: 4,
                py: 1.5,
                fontSize: '1rem',
                fontWeight: 600,
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                '&:hover': {
                  backgroundColor: '#1565c0',
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease',
              }}
            >
              LEARN MORE
            </Button>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '400px',
              }}
            >
              <img
                src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="School Campus"
                style={{
                  width: '100%',
                  height: '400px',
                  objectFit: 'cover',
                  borderRadius: '8px',
                  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                }}
              />
            </Box>
          </Grid>
        </Grid>

        {/* Quick Links Grid */}
        <Grid container spacing={3}>
          {quickLinks.map((link, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                onClick={() => scrollToSection(link.href)}
                sx={{
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
                  },
                  borderRadius: 2,
                  overflow: 'hidden',
                }}
              >
                <Box
                  sx={{
                    height: 200,
                    backgroundImage: `url(${link.image})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    position: 'relative',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(0,0,0,0.3)',
                      transition: 'all 0.3s ease',
                    },
                    '&:hover::before': {
                      backgroundColor: 'rgba(0,0,0,0.5)',
                    }
                  }}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 20,
                      left: 20,
                      right: 20,
                      zIndex: 1,
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        color: 'white',
                        fontWeight: 'bold',
                        textTransform: 'capitalize',
                        textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
                      }}
                    >
                      {link.title}
                    </Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  )
}

export default AboutSection
