import React from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
} from '@mui/material'

const CurriculumSection = () => {
  const curriculums = [
    {
      title: 'Engineering',
      description: 'NTSS has been offering Engineering programs since 2010.',
      color: '#1976d2',
    },
    {
      title: 'Computer Science',
      description: 'Computer Science courses focus on skills-based learning…',
      color: '#ff9800',
    },
    {
      title: 'Technical Skills',
      description: 'The Faculty of Technical Skills Development',
      color: '#4caf50',
    },
    {
      title: 'ICDL',
      description: 'The World\'s leading Computer Skills Certification',
      color: '#9c27b0',
    },
  ]

  return (
    <Box
      sx={{
        py: 8,
        backgroundColor: '#f8f9fa',
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {curriculums.map((curriculum, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
                  },
                  borderRadius: 2,
                  overflow: 'hidden',
                  position: 'relative',
                }}
              >
                {/* Color Bar */}
                <Box
                  sx={{
                    height: 6,
                    backgroundColor: curriculum.color,
                    width: '100%',
                  }}
                />
                
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 'bold',
                      mb: 2,
                      color: '#333',
                    }}
                  >
                    {curriculum.title}
                  </Typography>
                  
                  <Typography
                    variant="body1"
                    sx={{
                      color: '#666',
                      lineHeight: 1.6,
                    }}
                  >
                    {curriculum.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  )
}

export default CurriculumSection
