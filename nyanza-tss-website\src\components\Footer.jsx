import React from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  <PERSON>rid,
  Link,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
} from '@mui/material'
import {
  School,
  LocationOn,
  Phone,
  Email,
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
  YouTube,
} from '@mui/icons-material'

const Footer = () => {
  const quickLinks = [
    { label: 'About Us', href: '#about' },
    { label: 'Programs', href: '#programs' },
    { label: 'Admissions', href: '#admissions' },
    { label: 'Faculty', href: '#faculty' },
    { label: 'Student Life', href: '#student-life' },
    { label: 'Alumni', href: '#alumni' },
  ]

  const academicLinks = [
    { label: 'Academic Calendar', href: '#calendar' },
    { label: 'Course Catalog', href: '#catalog' },
    { label: 'Student Portal', href: '#portal' },
    { label: 'Library', href: '#library' },
    { label: 'Research', href: '#research' },
    { label: 'Career Services', href: '#careers' },
  ]

  const socialLinks = [
    { icon: <Facebook />, href: '#', label: 'Facebook' },
    { icon: <Twitter />, href: '#', label: 'Twitter' },
    { icon: <Instagram />, href: '#', label: 'Instagram' },
    { icon: <LinkedIn />, href: '#', label: 'LinkedIn' },
    { icon: <YouTube />, href: '#', label: 'YouTube' },
  ]

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: 'primary.main',
        color: 'white',
        pt: 6,
        pb: 3,
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* School Information */}
          <Grid item xs={12} md={4}>
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <School sx={{ fontSize: 32 }} />
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 'bold',
                  }}
                >
                  NTSS
                </Typography>
              </Box>
              <Typography
                variant="h6"
                sx={{
                  mb: 2,
                  fontWeight: 500,
                }}
              >
                Nyanza Technical Secondary School
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  mb: 3,
                  opacity: 0.9,
                  lineHeight: 1.6,
                }}
              >
                Empowering students with technical excellence and innovative education
                for over 15 years. Building tomorrow's leaders in engineering and technology.
              </Typography>
            </Box>

            {/* Contact Information */}
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  mb: 2,
                }}
              >
                Contact Information
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1 }}>
                <LocationOn sx={{ fontSize: 20, mt: 0.5, opacity: 0.8 }} />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Nyanza Province, Kenya<br />
                  P.O. Box 123-40100<br />
                  Kisumu, Kenya
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Phone sx={{ fontSize: 20, opacity: 0.8 }} />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  +254 700 123 456
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Email sx={{ fontSize: 20, opacity: 0.8 }} />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  <EMAIL>
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Quick Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                mb: 2,
              }}
            >
              Quick Links
            </Typography>
            <List dense sx={{ p: 0 }}>
              {quickLinks.map((link, index) => (
                <ListItem
                  key={index}
                  sx={{
                    p: 0,
                    mb: 0.5,
                  }}
                >
                  <Link
                    component="button"
                    onClick={() => scrollToSection(link.href)}
                    sx={{
                      color: 'white',
                      textDecoration: 'none',
                      opacity: 0.9,
                      fontSize: '0.9rem',
                      textAlign: 'left',
                      '&:hover': {
                        opacity: 1,
                        textDecoration: 'underline',
                      },
                    }}
                  >
                    {link.label}
                  </Link>
                </ListItem>
              ))}
            </List>
          </Grid>

          {/* Academic Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                mb: 2,
              }}
            >
              Academic
            </Typography>
            <List dense sx={{ p: 0 }}>
              {academicLinks.map((link, index) => (
                <ListItem
                  key={index}
                  sx={{
                    p: 0,
                    mb: 0.5,
                  }}
                >
                  <Link
                    component="button"
                    onClick={() => scrollToSection(link.href)}
                    sx={{
                      color: 'white',
                      textDecoration: 'none',
                      opacity: 0.9,
                      fontSize: '0.9rem',
                      textAlign: 'left',
                      '&:hover': {
                        opacity: 1,
                        textDecoration: 'underline',
                      },
                    }}
                  >
                    {link.label}
                  </Link>
                </ListItem>
              ))}
            </List>
          </Grid>

          {/* Social Media & Newsletter */}
          <Grid item xs={12} md={4}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                mb: 2,
              }}
            >
              Connect With Us
            </Typography>
            
            <Typography
              variant="body2"
              sx={{
                mb: 2,
                opacity: 0.9,
              }}
            >
              Follow us on social media for the latest updates and news
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
              {socialLinks.map((social, index) => (
                <IconButton
                  key={index}
                  href={social.href}
                  sx={{
                    color: 'white',
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    '&:hover': {
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                  aria-label={social.label}
                >
                  {social.icon}
                </IconButton>
              ))}
            </Box>

            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                mb: 1,
              }}
            >
              School Hours
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9, mb: 0.5 }}>
              Monday - Friday: 7:00 AM - 5:00 PM
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9, mb: 0.5 }}>
              Saturday: 8:00 AM - 12:00 PM
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Sunday: Closed
            </Typography>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4, backgroundColor: 'rgba(255,255,255,0.2)' }} />

        {/* Bottom Footer */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            © {new Date().getFullYear()} Nyanza Technical Secondary School. All rights reserved.
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 3 }}>
            <Link
              href="#"
              sx={{
                color: 'white',
                textDecoration: 'none',
                opacity: 0.8,
                fontSize: '0.9rem',
                '&:hover': {
                  opacity: 1,
                  textDecoration: 'underline',
                },
              }}
            >
              Privacy Policy
            </Link>
            <Link
              href="#"
              sx={{
                color: 'white',
                textDecoration: 'none',
                opacity: 0.8,
                fontSize: '0.9rem',
                '&:hover': {
                  opacity: 1,
                  textDecoration: 'underline',
                },
              }}
            >
              Terms of Service
            </Link>
            <Link
              href="#"
              sx={{
                color: 'white',
                textDecoration: 'none',
                opacity: 0.8,
                fontSize: '0.9rem',
                '&:hover': {
                  opacity: 1,
                  textDecoration: 'underline',
                },
              }}
            >
              Site Map
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  )
}

export default Footer
