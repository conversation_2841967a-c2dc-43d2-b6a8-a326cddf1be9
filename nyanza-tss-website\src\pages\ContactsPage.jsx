import React, { useState } from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
  Paper,
  IconButton,
} from '@mui/material'
import {
  Home,
  ContactMail,
  Phone,
  Email,
  LocationOn,
  Schedule,
  Person,
  School,
  Business,
  LocalHospital,
  Computer,
  SportsVolleyball,
  Restaurant,
  DirectionsBus,
  Send,
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
  WhatsApp,
  Telegram,
} from '@mui/icons-material'

const ContactsPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    department: '',
    message: ''
  })

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
  }

  const contactInfo = {
    address: 'Kigoma Sector, Butansinda Cell, Nyanza District, Southern Province, Rwanda',
    phone: '+250 788 309 436',
    email: '<EMAIL>',
    website: 'www.nyanzatss.ac.rw',
    principal: 'Manirambona Leonard',
    principalPhone: '+250 788 309 436'
  }

  const departments = [
    {
      name: 'Principal\'s Office',
      head: 'Manirambona Leonard',
      phone: '+250 788 309 436',
      email: '<EMAIL>',
      icon: <Person />,
      color: '#1976d2',
      description: 'School leadership and administration'
    },
    {
      name: 'Academic Affairs',
      head: 'Dr. Marie Uwimana',
      phone: '+250 788 309 437',
      email: '<EMAIL>',
      icon: <School />,
      color: '#4caf50',
      description: 'Curriculum, assessments, and academic programs'
    },
    {
      name: 'Student Affairs',
      head: 'Jean Baptiste Nkurunziza',
      phone: '+250 788 309 438',
      email: '<EMAIL>',
      icon: <Group />,
      color: '#ff9800',
      description: 'Student services, clubs, and activities'
    },
    {
      name: 'Admissions Office',
      head: 'Grace Mukamana',
      phone: '+250 788 309 439',
      email: '<EMAIL>',
      icon: <Business />,
      color: '#9c27b0',
      description: 'Applications, enrollment, and fees'
    },
    {
      name: 'IT Department',
      head: 'Patrick Habimana',
      phone: '+250 788 309 440',
      email: '<EMAIL>',
      icon: <Computer />,
      color: '#2196f3',
      description: 'Technical support and computer services'
    },
    {
      name: 'Health Services',
      head: 'Nurse Alice Nyirahabimana',
      phone: '+250 788 309 441',
      email: '<EMAIL>',
      icon: <LocalHospital />,
      color: '#f44336',
      description: 'Medical care and health programs'
    },
    {
      name: 'Sports Department',
      head: 'Coach Emmanuel Nzeyimana',
      phone: '+250 788 309 442',
      email: '<EMAIL>',
      icon: <SportsVolleyball />,
      color: '#4caf50',
      description: 'Athletics and sports programs'
    },
    {
      name: 'Cafeteria Services',
      head: 'Manager Josephine Mukamana',
      phone: '+250 788 309 443',
      email: '<EMAIL>',
      icon: <Restaurant />,
      color: '#795548',
      description: 'Food services and nutrition'
    }
  ]

  const officeHours = [
    { day: 'Monday - Friday', hours: '7:00 AM - 5:00 PM', type: 'Regular Hours' },
    { day: 'Saturday', hours: '8:00 AM - 12:00 PM', type: 'Limited Services' },
    { day: 'Sunday', hours: 'Closed', type: 'Weekend' },
    { day: 'Public Holidays', hours: 'Closed', type: 'Holiday' }
  ]

  const socialMedia = [
    { name: 'Facebook', icon: <Facebook />, color: '#1877f2', handle: '@NyanzaTSS' },
    { name: 'Twitter', icon: <Twitter />, color: '#1da1f2', handle: '@NyanzaTSS' },
    { name: 'Instagram', icon: <Instagram />, color: '#e4405f', handle: '@nyanzatss' },
    { name: 'LinkedIn', icon: <LinkedIn />, color: '#0077b5', handle: 'Nyanza Technical Secondary School' },
    { name: 'WhatsApp', icon: <WhatsApp />, color: '#25d366', handle: '+250 788 309 436' },
    { name: 'Telegram', icon: <Telegram />, color: '#0088cc', handle: '@NyanzaTSS' }
  ]

  const emergencyContacts = [
    { service: 'Emergency Line', number: '+250 788 309 444', available: '24/7' },
    { service: 'Security Office', number: '+250 788 309 445', available: '24/7' },
    { service: 'Health Emergency', number: '+250 788 309 446', available: '24/7' },
    { service: 'Transport Emergency', number: '+250 788 309 447', available: 'School Hours' }
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5)), url("https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 25% 75%, rgba(25, 118, 210, 0.3) 0%, transparent 50%), radial-gradient(circle at 75% 25%, rgba(76, 175, 80, 0.3) 0%, transparent 50%)',
            animation: 'float 22s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-25px) rotate(2deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <ContactMail sx={{ mr: 0.5 }} fontSize="inherit" />
              Contact Us
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Contact Us
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Get in touch with Nyanza Technical Secondary School. We're here to help with 
            admissions, academic inquiries, and any questions about our programs.
          </Typography>
        </Container>
      </Box>

      {/* Quick Contact Info */}
      <Container maxWidth="lg" sx={{ py: { xs: 4, md: 6 } }}>
        <Grid container spacing={3}>
          {[
            { icon: <LocationOn />, title: 'Address', info: contactInfo.address, color: '#1976d2' },
            { icon: <Phone />, title: 'Phone', info: contactInfo.phone, color: '#4caf50' },
            { icon: <Email />, title: 'Email', info: contactInfo.email, color: '#ff9800' },
            { icon: <Person />, title: 'Principal', info: contactInfo.principal, color: '#9c27b0' }
          ].map((contact, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={1200 + index * 100}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${contact.color}10 0%, ${contact.color}05 100%)`,
                    borderRadius: 3,
                    border: `2px solid ${contact.color}30`,
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: `0 20px 60px ${contact.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: contact.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        boxShadow: `0 8px 25px ${contact.color}40`,
                        animation: 'pulse 2s ease-in-out infinite',
                        '@keyframes pulse': {
                          '0%, 100%': { transform: 'scale(1)' },
                          '50%': { transform: 'scale(1.05)' },
                        },
                      }}
                    >
                      {contact.icon}
                    </Box>
                    <Typography variant="h6" fontWeight={600} color={contact.color} mb={1}>
                      {contact.title}
                    </Typography>
                    <Typography variant="body2" color="#64748b" fontWeight={500} sx={{ lineHeight: 1.4 }}>
                      {contact.info}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Contact Form and Map */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Grid container spacing={6}>
          {/* Contact Form */}
          <Grid item xs={12} md={6}>
            <Fade in timeout={1600}>
              <Card
                sx={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 3,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 700,
                      mb: 3,
                      color: '#1e293b',
                      textAlign: 'center',
                    }}
                  >
                    Send us a Message
                  </Typography>

                  <Box component="form" onSubmit={handleSubmit}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Full Name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                          variant="outlined"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Email Address"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          variant="outlined"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Phone Number"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          variant="outlined"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel>Department</InputLabel>
                          <Select
                            name="department"
                            value={formData.department}
                            onChange={handleInputChange}
                            label="Department"
                          >
                            <MenuItem value="">Select Department</MenuItem>
                            {departments.map((dept, index) => (
                              <MenuItem key={index} value={dept.name}>
                                {dept.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleInputChange}
                          required
                          variant="outlined"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Message"
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          required
                          multiline
                          rows={4}
                          variant="outlined"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <Button
                          type="submit"
                          variant="contained"
                          size="large"
                          startIcon={<Send />}
                          fullWidth
                          sx={{
                            bgcolor: '#1976d2',
                            color: 'white',
                            fontWeight: 600,
                            py: 1.5,
                            borderRadius: 2,
                            textTransform: 'none',
                            fontSize: '1.1rem',
                            '&:hover': {
                              bgcolor: '#1565c0',
                            },
                          }}
                        >
                          Send Message
                        </Button>
                      </Grid>
                    </Grid>
                  </Box>
                </CardContent>
              </Card>
            </Fade>
          </Grid>

          {/* Map and Office Hours */}
          <Grid item xs={12} md={6}>
            <Grid container spacing={3}>
              {/* Map Placeholder */}
              <Grid item xs={12}>
                <Grow in timeout={1800}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      overflow: 'hidden',
                    }}
                  >
                    <Box
                      sx={{
                        height: 250,
                        background: 'linear-gradient(135deg, #1976d2 0%, #4caf50 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        position: 'relative',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          backgroundImage: 'url("https://images.unsplash.com/photo-1524661135-423995f22d0b?w=600&h=400&fit=crop")',
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                          opacity: 0.3,
                        },
                      }}
                    >
                      <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
                        <LocationOn sx={{ fontSize: '3rem', mb: 1 }} />
                        <Typography variant="h6" fontWeight={600}>
                          Find Us on Map
                        </Typography>
                        <Typography variant="body2">
                          Kigoma Sector, Nyanza District
                        </Typography>
                      </Box>
                    </Box>
                  </Card>
                </Grow>
              </Grid>

              {/* Office Hours */}
              <Grid item xs={12}>
                <Grow in timeout={2000}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    }}
                  >
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Schedule sx={{ color: '#1976d2', mr: 2, fontSize: '2rem' }} />
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 600,
                            color: '#1e293b',
                          }}
                        >
                          Office Hours
                        </Typography>
                      </Box>

                      <List>
                        {officeHours.map((hours, index) => (
                          <ListItem key={index} sx={{ px: 0, py: 1 }}>
                            <ListItemText
                              primary={hours.day}
                              secondary={hours.hours}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontWeight: 500,
                                  color: '#1e293b',
                                },
                                '& .MuiListItemText-secondary': {
                                  color: '#64748b',
                                },
                              }}
                            />
                            <Chip
                              label={hours.type}
                              size="small"
                              sx={{
                                bgcolor: index === 2 || index === 3 ? '#f4433620' : '#4caf5020',
                                color: index === 2 || index === 3 ? '#f44336' : '#4caf50',
                                fontWeight: 500,
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Container>

      {/* Department Contacts */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.05), rgba(0,0,0,0.02)), url("https://images.unsplash.com/photo-1497366216548-37526070297c?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2200}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Department Contacts
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Connect directly with the right department for your specific needs
          </Typography>

          <Grid container spacing={4}>
            {departments.map((department, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Grow in timeout={2400 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 3, textAlign: 'center' }}>
                      <Box
                        sx={{
                          width: 60,
                          height: 60,
                          bgcolor: department.color,
                          borderRadius: 3,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          mx: 'auto',
                          mb: 2,
                          boxShadow: `0 8px 25px ${department.color}40`,
                        }}
                      >
                        {department.icon}
                      </Box>

                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                          fontSize: '1rem',
                        }}
                      >
                        {department.name}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          mb: 2,
                          fontSize: '0.8rem',
                          lineHeight: 1.4,
                        }}
                      >
                        {department.description}
                      </Typography>

                      <Divider sx={{ my: 2 }} />

                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                          fontSize: '0.85rem',
                        }}
                      >
                        {department.head}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          color: department.color,
                          mb: 0.5,
                          fontSize: '0.8rem',
                          fontWeight: 500,
                        }}
                      >
                        {department.phone}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          fontSize: '0.8rem',
                        }}
                      >
                        {department.email}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Emergency Contacts & Social Media */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Grid container spacing={6}>
          {/* Emergency Contacts */}
          <Grid item xs={12} md={6}>
            <Fade in timeout={2600}>
              <Card
                sx={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 3,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <Phone sx={{ color: '#f44336', mr: 2, fontSize: '2rem' }} />
                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 600,
                        color: '#1e293b',
                      }}
                    >
                      Emergency Contacts
                    </Typography>
                  </Box>

                  <List>
                    {emergencyContacts.map((contact, index) => (
                      <ListItem key={index} sx={{ px: 0, py: 1.5 }}>
                        <ListItemText
                          primary={contact.service}
                          secondary={contact.number}
                          sx={{
                            '& .MuiListItemText-primary': {
                              fontWeight: 500,
                              color: '#1e293b',
                              fontSize: '1rem',
                            },
                            '& .MuiListItemText-secondary': {
                              color: '#f44336',
                              fontWeight: 600,
                              fontSize: '1.1rem',
                            },
                          }}
                        />
                        <Chip
                          label={contact.available}
                          size="small"
                          sx={{
                            bgcolor: contact.available === '24/7' ? '#4caf5020' : '#ff980020',
                            color: contact.available === '24/7' ? '#4caf50' : '#ff9800',
                            fontWeight: 500,
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Fade>
          </Grid>

          {/* Social Media */}
          <Grid item xs={12} md={6}>
            <Fade in timeout={2800}>
              <Card
                sx={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 3,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 3,
                      textAlign: 'center',
                    }}
                  >
                    Follow Us on Social Media
                  </Typography>

                  <Grid container spacing={3}>
                    {socialMedia.map((social, index) => (
                      <Grid item xs={12} sm={6} key={index}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            p: 2,
                            borderRadius: 2,
                            border: `2px solid ${social.color}30`,
                            background: `${social.color}10`,
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                            '&:hover': {
                              transform: 'translateY(-3px)',
                              boxShadow: `0 8px 25px ${social.color}30`,
                            },
                          }}
                        >
                          <IconButton
                            sx={{
                              bgcolor: social.color,
                              color: 'white',
                              mr: 2,
                              '&:hover': {
                                bgcolor: social.color,
                                filter: 'brightness(0.9)',
                              },
                            }}
                          >
                            {social.icon}
                          </IconButton>
                          <Box>
                            <Typography
                              variant="body1"
                              sx={{
                                fontWeight: 600,
                                color: '#1e293b',
                                fontSize: '0.9rem',
                              }}
                            >
                              {social.name}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                color: '#64748b',
                                fontSize: '0.8rem',
                              }}
                            >
                              {social.handle}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </Fade>
          </Grid>
        </Grid>
      </Container>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6)), url("https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={3000}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
              }}
            >
              Visit Our Campus
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Experience Nyanza TSS firsthand. Schedule a campus tour and discover our
            world-class facilities and vibrant learning environment.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<LocationOn />}
              sx={{
                bgcolor: 'white',
                color: '#1976d2',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Schedule a Visit
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<Phone />}
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              Call Now
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default ContactsPage
