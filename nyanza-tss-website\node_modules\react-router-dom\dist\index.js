/**
 * react-router-dom v7.6.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var react_router_dom_exports = {};
__export(react_router_dom_exports, {
  HydratedRouter: () => import_dom.HydratedRouter,
  RouterProvider: () => import_dom.RouterProvider
});
module.exports = __toCommonJS(react_router_dom_exports);
var import_dom = require("react-router/dom");
__reExport(react_router_dom_exports, require("react-router"), module.exports);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  HydratedRouter,
  RouterProvider,
  ...require("react-router")
});
