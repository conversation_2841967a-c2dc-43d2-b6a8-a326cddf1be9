# @mui/lab

This package hosts the incubator components that are not yet ready to move to `core`.

## Installation

Install the package in your project directory with:

<!-- #npm-tag-reference -->

```bash
npm install @mui/lab
```

The lab has peer dependencies on the Material Design components and on the Emotion library.
If you are not already using them in your project, you can install with:

<!-- #npm-tag-reference -->

```bash
npm install @mui/material @emotion/react @emotion/styled
```

## Documentation

<!-- #host-reference -->

Visit [https://mui.com/material-ui/about-the-lab/](https://mui.com/material-ui/about-the-lab/) to view the full documentation.
