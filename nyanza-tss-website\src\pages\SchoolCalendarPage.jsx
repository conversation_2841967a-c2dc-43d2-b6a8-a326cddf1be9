import React, { useState } from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Breadc<PERSON>bs,
  Link,
  Fade,
  <PERSON>row,
  Chip,
  Button,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material'
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from '@mui/lab'
import {
  Home,
  CalendarToday,
  Event,
  School,
  EmojiEvents,
  Assignment,
  Celebration,
  ExpandMore,
  CheckCircle,
  Star,
  Group,
  Science,
  Computer,
  Build,
  SportsVolleyball,
  MenuBook,
  Notifications,
} from '@mui/icons-material'

const SchoolCalendarPage = () => {
  const [selectedSemester, setSelectedSemester] = useState(0)

  const semesters = ['Academic Year 2024-2025', 'Semester 1', 'Semester 2', 'Semester 3']

  const academicEvents = [
    {
      date: 'January 15, 2024',
      title: 'Semester 1 Begins',
      type: 'Academic',
      description: 'First semester classes commence for all levels',
      icon: <School />,
      color: '#1976d2',
      image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=400&h=200&fit=crop'
    },
    {
      date: 'February 14, 2024',
      title: 'Technical Skills Competition',
      type: 'Competition',
      description: 'Inter-school technical skills showcase and competition',
      icon: <EmojiEvents />,
      color: '#ffd700',
      image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=200&fit=crop'
    },
    {
      date: 'March 20, 2024',
      title: 'Mid-Semester Examinations',
      type: 'Examination',
      description: 'Mid-semester assessments for all programs',
      icon: <Assignment />,
      color: '#ed6c02',
      image: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=200&fit=crop'
    },
    {
      date: 'April 7, 2024',
      title: 'Genocide Memorial Week',
      type: 'Memorial',
      description: 'Week of remembrance and unity activities',
      icon: <Celebration />,
      color: '#9c27b0',
      image: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=200&fit=crop'
    },
    {
      date: 'May 15, 2024',
      title: 'Industry Internship Program',
      type: 'Internship',
      description: 'Level 5 students begin industry placements',
      icon: <Group />,
      color: '#2e7d32',
      image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=200&fit=crop'
    },
    {
      date: 'June 10, 2024',
      title: 'Semester 1 Final Exams',
      type: 'Examination',
      description: 'Final examinations and practical assessments',
      icon: <Assignment />,
      color: '#d32f2f',
      image: 'https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=400&h=200&fit=crop'
    },
    {
      date: 'July 1, 2024',
      title: 'Semester Break',
      type: 'Holiday',
      description: 'Mid-year break and vacation period',
      icon: <Celebration />,
      color: '#ff9800',
      image: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=200&fit=crop'
    },
    {
      date: 'August 5, 2024',
      title: 'Semester 2 Begins',
      type: 'Academic',
      description: 'Second semester classes commence',
      icon: <School />,
      color: '#1976d2',
      image: 'https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=400&h=200&fit=crop'
    },
    {
      date: 'September 20, 2024',
      title: 'National Volleyball Championship',
      type: 'Sports',
      description: 'Nyanza TSS volleyball team competes nationally',
      icon: <SportsVolleyball />,
      color: '#4caf50',
      image: 'https://images.unsplash.com/photo-1612872087720-bb876e2e67d1?w=400&h=200&fit=crop'
    },
    {
      date: 'October 15, 2024',
      title: 'Innovation Showcase',
      type: 'Innovation',
      description: 'Student projects and innovations exhibition',
      icon: <Science />,
      color: '#9c27b0',
      image: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=200&fit=crop'
    },
    {
      date: 'November 10, 2024',
      title: 'Career Fair',
      type: 'Career',
      description: 'Industry partners recruitment and networking event',
      icon: <Group />,
      color: '#607d8b',
      image: 'https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=400&h=200&fit=crop'
    },
    {
      date: 'December 15, 2024',
      title: 'Graduation Ceremony',
      type: 'Graduation',
      description: 'Celebration of graduating students achievements',
      icon: <Celebration />,
      color: '#ffd700',
      image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=400&h=200&fit=crop'
    }
  ]

  const importantDates = [
    {
      category: 'Registration Deadlines',
      dates: [
        { date: 'December 20, 2023', event: 'New Student Registration Opens' },
        { date: 'January 10, 2024', event: 'Course Registration Deadline' },
        { date: 'July 15, 2024', event: 'Semester 2 Registration' },
      ],
      color: '#1976d2'
    },
    {
      category: 'Examination Periods',
      dates: [
        { date: 'March 18-22, 2024', event: 'Mid-Semester Exams' },
        { date: 'June 8-15, 2024', event: 'Semester 1 Finals' },
        { date: 'November 25-30, 2024', event: 'Semester 2 Finals' },
      ],
      color: '#ed6c02'
    },
    {
      category: 'Holiday Periods',
      dates: [
        { date: 'April 7-14, 2024', event: 'Genocide Memorial Week' },
        { date: 'July 1-31, 2024', event: 'Mid-Year Break' },
        { date: 'December 16-31, 2024', event: 'End of Year Holiday' },
      ],
      color: '#4caf50'
    },
    {
      category: 'Special Events',
      dates: [
        { date: 'February 14, 2024', event: 'Technical Skills Day' },
        { date: 'May 1, 2024', event: 'Labour Day Celebration' },
        { date: 'October 1, 2024', event: 'Heroes Day' },
      ],
      color: '#9c27b0'
    }
  ]

  const monthlyHighlights = [
    {
      month: 'January',
      highlights: ['New semester begins', 'Orientation week', 'Club registrations'],
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop'
    },
    {
      month: 'February',
      highlights: ['Technical competitions', 'Valentine\'s Day events', 'Career workshops'],
      image: 'https://images.unsplash.com/photo-1549057446-9f5c6ac91a04?w=300&h=200&fit=crop'
    },
    {
      month: 'March',
      highlights: ['Mid-semester exams', 'Women\'s Day celebration', 'Spring activities'],
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop'
    },
    {
      month: 'April',
      highlights: ['Genocide memorial', 'Unity activities', 'Community service'],
      image: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=300&h=200&fit=crop'
    },
    {
      month: 'May',
      highlights: ['Internship placements', 'Labour Day', 'Project presentations'],
      image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=300&h=200&fit=crop'
    },
    {
      month: 'June',
      highlights: ['Final examinations', 'Graduation preparations', 'Summer planning'],
      image: 'https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=300&h=200&fit=crop'
    }
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5)), url("https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 30% 70%, rgba(25, 118, 210, 0.3) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(156, 39, 176, 0.3) 0%, transparent 50%)',
            animation: 'float 20s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-20px) rotate(1deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <CalendarToday sx={{ mr: 0.5 }} fontSize="inherit" />
              School Calendar
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Academic Calendar 2024-2025
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Stay informed about important dates, events, and academic milestones throughout the year. 
            Plan your academic journey with our comprehensive calendar.
          </Typography>
        </Container>
      </Box>

      {/* Quick Stats */}
      <Container maxWidth="lg" sx={{ py: { xs: 4, md: 6 } }}>
        <Grid container spacing={3}>
          {[
            { label: 'Academic Events', value: '45+', icon: <School />, color: '#1976d2' },
            { label: 'Competitions', value: '12', icon: <EmojiEvents />, color: '#ffd700' },
            { label: 'Cultural Events', value: '8', icon: <Celebration />, color: '#9c27b0' },
            { label: 'Sports Events', value: '15', icon: <SportsVolleyball />, color: '#4caf50' },
          ].map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={1200 + index * 100}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${stat.color}10 0%, ${stat.color}05 100%)`,
                    borderRadius: 3,
                    border: `2px solid ${stat.color}30`,
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: `0 20px 60px ${stat.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: stat.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        boxShadow: `0 8px 25px ${stat.color}40`,
                        animation: 'pulse 2s ease-in-out infinite',
                        '@keyframes pulse': {
                          '0%, 100%': { transform: 'scale(1)' },
                          '50%': { transform: 'scale(1.05)' },
                        },
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Typography variant="h3" fontWeight={700} color={stat.color} mb={1}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="#64748b" fontWeight={500}>
                      {stat.label}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Academic Events Timeline */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={1600}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Major Academic Events
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Key dates and events that shape your academic year at Nyanza TSS
        </Typography>

        <Timeline position="alternate">
          {academicEvents.map((event, index) => (
            <TimelineItem key={index}>
              <TimelineOppositeContent
                sx={{ m: 'auto 0' }}
                align={index % 2 === 0 ? 'right' : 'left'}
                variant="body2"
                color="#64748b"
                fontWeight={600}
              >
                {event.date}
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineConnector sx={{ bgcolor: event.color }} />
                <TimelineDot
                  sx={{
                    bgcolor: event.color,
                    color: 'white',
                    width: 60,
                    height: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: `0 8px 25px ${event.color}40`,
                    animation: `pulse 2s ease-in-out infinite ${index * 0.2}s`,
                    '@keyframes pulse': {
                      '0%, 100%': { transform: 'scale(1)' },
                      '50%': { transform: 'scale(1.1)' },
                    },
                  }}
                >
                  {event.icon}
                </TimelineDot>
                <TimelineConnector sx={{ bgcolor: event.color }} />
              </TimelineSeparator>
              <TimelineContent sx={{ py: '12px', px: 2 }}>
                <Grow in timeout={1800 + index * 200}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      overflow: 'hidden',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <Box
                      sx={{
                        height: 120,
                        backgroundImage: `linear-gradient(45deg, ${event.color}80, ${event.color}60), url("${event.image}")`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        display: 'flex',
                        alignItems: 'flex-end',
                        p: 2,
                      }}
                    >
                      <Chip
                        label={event.type}
                        sx={{
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          color: event.color,
                          fontWeight: 600,
                        }}
                      />
                    </Box>
                    <CardContent sx={{ p: 3 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 1,
                        }}
                      >
                        {event.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          lineHeight: 1.6,
                        }}
                      >
                        {event.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grow>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      </Container>

      {/* Important Dates by Category */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.05), rgba(0,0,0,0.02)), url("https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2000}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Important Dates
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Key deadlines and dates organized by category for easy reference
          </Typography>

          <Grid container spacing={4}>
            {importantDates.map((category, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Grow in timeout={2200 + index * 200}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: category.color,
                          mb: 3,
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            bgcolor: category.color,
                            borderRadius: '50%',
                            mr: 2,
                            animation: 'pulse 2s ease-in-out infinite',
                            '@keyframes pulse': {
                              '0%, 100%': { transform: 'scale(1)', opacity: 1 },
                              '50%': { transform: 'scale(1.2)', opacity: 0.7 },
                            },
                          }}
                        />
                        {category.category}
                      </Typography>

                      <List dense>
                        {category.dates.map((dateItem, dateIndex) => (
                          <ListItem key={dateIndex} sx={{ px: 0, py: 1 }}>
                            <ListItemIcon sx={{ minWidth: 40 }}>
                              <CheckCircle sx={{ fontSize: '1.2rem', color: category.color }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={dateItem.event}
                              secondary={dateItem.date}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.95rem',
                                  fontWeight: 500,
                                  color: '#1e293b',
                                },
                                '& .MuiListItemText-secondary': {
                                  fontSize: '0.85rem',
                                  color: '#64748b',
                                  fontWeight: 600,
                                },
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Monthly Highlights */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2400}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Monthly Highlights
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Discover what makes each month special at Nyanza TSS
        </Typography>

        <Grid container spacing={4}>
          {monthlyHighlights.map((month, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Grow in timeout={2600 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-12px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <Box
                    sx={{
                      height: 200,
                      backgroundImage: `linear-gradient(45deg, rgba(25, 118, 210, 0.8), rgba(156, 39, 176, 0.6)), url("${month.image}")`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative',
                    }}
                  >
                    <Typography
                      variant="h4"
                      sx={{
                        color: 'white',
                        fontWeight: 700,
                        textShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
                        animation: 'float 3s ease-in-out infinite',
                        '@keyframes float': {
                          '0%, 100%': { transform: 'translateY(0px)' },
                          '50%': { transform: 'translateY(-10px)' },
                        },
                      }}
                    >
                      {month.month}
                    </Typography>
                  </Box>

                  <CardContent sx={{ p: 4 }}>
                    <List dense>
                      {month.highlights.map((highlight, highlightIndex) => (
                        <ListItem key={highlightIndex} sx={{ px: 0, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 30 }}>
                            <Star sx={{ fontSize: '1rem', color: '#ffd700' }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={highlight}
                            sx={{
                              '& .MuiListItemText-primary': {
                                fontSize: '0.9rem',
                                color: '#64748b',
                                fontWeight: 500,
                              },
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6)), url("https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={2800}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
              }}
            >
              Stay Connected with Campus Life
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Never miss an important date or exciting event. Subscribe to our calendar updates
            and stay informed about everything happening at Nyanza TSS.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<Notifications />}
              sx={{
                bgcolor: 'white',
                color: '#1976d2',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Subscribe to Updates
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<CalendarToday />}
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              Download Calendar
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default SchoolCalendarPage
