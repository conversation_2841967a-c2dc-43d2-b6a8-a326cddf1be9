import React from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Button,
  LinearProgress,
  Avatar,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/material'
import {
  Home,
  Computer,
  Code,
  Web,
  Storage,
  Security,
  CloudQueue,
  Android,
  Apple,
  CheckCircle,
  TrendingUp,
  Work,
  School,
  EmojiEvents,
  Group,
  Star,
} from '@mui/icons-material'

const ComputerSciencePage = () => {
  const programLevels = [
    {
      level: 'Level 3 - Certificate',
      duration: '1 Year',
      description: 'Foundation in computer basics and programming',
      modules: [
        'Computer Fundamentals',
        'Basic Programming (Python)',
        'Web Design Basics',
        'Microsoft Office Suite',
        'Computer Networks Basics'
      ],
      color: '#1976d2'
    },
    {
      level: 'Level 4 - Diploma',
      duration: '2 Years',
      description: 'Comprehensive software development and IT skills',
      modules: [
        'Object-Oriented Programming (Java)',
        'Database Design & Management',
        'Web Development (HTML, CSS, JavaScript)',
        'Mobile App Development',
        'System Administration',
        'Software Engineering Principles'
      ],
      color: '#2e7d32'
    },
    {
      level: 'Level 5 - Advanced Diploma',
      duration: '3 Years',
      description: 'Advanced programming and software architecture',
      modules: [
        'Advanced Software Engineering',
        'Cloud Computing & DevOps',
        'Artificial Intelligence & Machine Learning',
        'Cybersecurity',
        'Enterprise Application Development',
        'Project Management',
        'Industry Internship'
      ],
      color: '#ed6c02'
    }
  ]

  const technologies = [
    { name: 'Python', level: 95, icon: '🐍', color: '#3776ab' },
    { name: 'Java', level: 90, icon: '☕', color: '#f89820' },
    { name: 'JavaScript', level: 88, icon: '🟨', color: '#f7df1e' },
    { name: 'React', level: 85, icon: '⚛️', color: '#61dafb' },
    { name: 'Node.js', level: 82, icon: '🟢', color: '#339933' },
    { name: 'MySQL', level: 87, icon: '🗄️', color: '#4479a1' },
    { name: 'MongoDB', level: 80, icon: '🍃', color: '#47a248' },
    { name: 'AWS', level: 75, icon: '☁️', color: '#ff9900' },
  ]

  const careerPaths = [
    {
      title: 'Software Developer',
      description: 'Build applications and software solutions',
      salary: '800,000 - 1,500,000 RWF/month',
      demand: 'High',
      icon: <Code />,
      color: '#1976d2',
      skills: ['Programming', 'Problem Solving', 'Testing']
    },
    {
      title: 'Web Developer',
      description: 'Create websites and web applications',
      salary: '600,000 - 1,200,000 RWF/month',
      demand: 'Very High',
      icon: <Web />,
      color: '#2e7d32',
      skills: ['HTML/CSS', 'JavaScript', 'Frameworks']
    },
    {
      title: 'Database Administrator',
      description: 'Manage and optimize database systems',
      salary: '700,000 - 1,300,000 RWF/month',
      demand: 'High',
      icon: <Storage />,
      color: '#ed6c02',
      skills: ['SQL', 'Database Design', 'Performance Tuning']
    },
    {
      title: 'Cybersecurity Specialist',
      description: 'Protect systems from security threats',
      salary: '900,000 - 1,800,000 RWF/month',
      demand: 'Very High',
      icon: <Security />,
      color: '#d32f2f',
      skills: ['Security Protocols', 'Risk Assessment', 'Incident Response']
    },
    {
      title: 'Mobile App Developer',
      description: 'Develop mobile applications for iOS and Android',
      salary: '750,000 - 1,400,000 RWF/month',
      demand: 'High',
      icon: <Android />,
      color: '#9c27b0',
      skills: ['Mobile Frameworks', 'UI/UX', 'API Integration']
    },
    {
      title: 'Cloud Engineer',
      description: 'Design and manage cloud infrastructure',
      salary: '1,000,000 - 2,000,000 RWF/month',
      demand: 'Very High',
      icon: <CloudQueue />,
      color: '#ff9800',
      skills: ['AWS/Azure', 'DevOps', 'Infrastructure as Code']
    }
  ]

  const facilities = [
    {
      name: 'Computer Laboratory',
      description: '40 high-performance computers with latest software',
      features: ['Intel i7 processors', 'SSD storage', 'Dual monitors', '24/7 access']
    },
    {
      name: 'Software Development Lab',
      description: 'Dedicated space for coding and project development',
      features: ['Development tools', 'Version control systems', 'Testing environments', 'Collaboration tools']
    },
    {
      name: 'Networking Lab',
      description: 'Hands-on networking equipment and simulation tools',
      features: ['Cisco equipment', 'Network simulators', 'Security tools', 'Server room access']
    },
    {
      name: 'Innovation Hub',
      description: 'Creative space for student projects and startups',
      features: ['3D printers', 'IoT devices', 'Prototyping tools', 'Mentorship programs']
    }
  ]

  const achievements = [
    {
      title: 'National Programming Competition',
      description: 'Students ranked 2nd in Rwanda Coding Challenge 2024',
      year: '2024',
      icon: <EmojiEvents />,
      color: '#ffd700'
    },
    {
      title: 'Industry Partnership',
      description: 'Collaboration with 15+ tech companies for internships',
      year: '2024',
      icon: <Group />,
      color: '#1976d2'
    },
    {
      title: 'Graduate Employment Rate',
      description: '95% of graduates employed within 6 months',
      year: '2023',
      icon: <TrendingUp />,
      color: '#2e7d32'
    },
    {
      title: 'Innovation Award',
      description: 'Best Technical Education Program in Southern Province',
      year: '2023',
      icon: <Star />,
      color: '#ed6c02'
    }
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Background */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.3) 0%, transparent 50%)',
            animation: 'float 20s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-20px) rotate(2deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <Computer sx={{ mr: 0.5 }} fontSize="inherit" />
              Computer Science
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Computer Science & IT
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Master the art of programming and technology. Our comprehensive Computer Science program 
            prepares you for the digital future with cutting-edge curriculum and hands-on experience.
          </Typography>
        </Container>
      </Box>

      {/* Program Levels */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={1200}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Program Structure
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Progressive learning path from basic computer skills to advanced software engineering
        </Typography>

        <Grid container spacing={4}>
          {programLevels.map((program, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Grow in timeout={1400 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-12px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 4,
                      background: `linear-gradient(90deg, ${program.color} 0%, ${program.color}80 100%)`,
                      animation: 'shimmer 2s ease-in-out infinite',
                      '@keyframes shimmer': {
                        '0%, 100%': { opacity: 0.8 },
                        '50%': { opacity: 1 },
                      },
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box sx={{ textAlign: 'center', mb: 3 }}>
                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 700,
                          color: program.color,
                          mb: 1,
                        }}
                      >
                        {program.level}
                      </Typography>
                      <Chip
                        label={program.duration}
                        sx={{
                          bgcolor: `${program.color}20`,
                          color: program.color,
                          fontWeight: 600,
                        }}
                      />
                    </Box>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        mb: 3,
                        textAlign: 'center',
                        lineHeight: 1.6,
                      }}
                    >
                      {program.description}
                    </Typography>

                    <Typography
                      variant="subtitle2"
                      sx={{
                        fontWeight: 600,
                        color: '#1e293b',
                        mb: 2,
                      }}
                    >
                      Key Modules:
                    </Typography>

                    <List dense>
                      {program.modules.map((module, moduleIndex) => (
                        <ListItem key={moduleIndex} sx={{ px: 0, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 30 }}>
                            <CheckCircle sx={{ fontSize: '1rem', color: program.color }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={module}
                            sx={{
                              '& .MuiListItemText-primary': {
                                fontSize: '0.85rem',
                                color: '#64748b',
                              },
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Technologies & Skills */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={1800}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Technologies You'll Master
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Learn industry-standard technologies and programming languages used by top companies
          </Typography>

          <Grid container spacing={3}>
            {technologies.map((tech, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Grow in timeout={2000 + index * 100}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 3, textAlign: 'center' }}>
                      <Typography variant="h3" sx={{ mb: 1 }}>
                        {tech.icon}
                      </Typography>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          mb: 2,
                        }}
                      >
                        {tech.name}
                      </Typography>
                      <Box sx={{ mb: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={tech.level}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            bgcolor: 'rgba(0,0,0,0.1)',
                            '& .MuiLinearProgress-bar': {
                              bgcolor: tech.color,
                              borderRadius: 4,
                            },
                          }}
                        />
                      </Box>
                      <Typography variant="body2" color="#64748b">
                        {tech.level}% Proficiency
                      </Typography>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Career Opportunities */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={2200}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Career Opportunities
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Explore exciting career paths in the rapidly growing technology sector
        </Typography>

        <Grid container spacing={4}>
          {careerPaths.map((career, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Grow in timeout={2400 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box
                      sx={{
                        width: 70,
                        height: 70,
                        bgcolor: career.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mb: 3,
                        boxShadow: `0 8px 25px ${career.color}40`,
                      }}
                    >
                      {career.icon}
                    </Box>

                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        color: '#1e293b',
                      }}
                    >
                      {career.title}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        mb: 3,
                        lineHeight: 1.6,
                      }}
                    >
                      {career.description}
                    </Typography>

                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        <strong>Salary Range:</strong> {career.salary}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Typography variant="body2" sx={{ mr: 1 }}>
                          <strong>Demand:</strong>
                        </Typography>
                        <Chip
                          label={career.demand}
                          size="small"
                          sx={{
                            bgcolor: career.demand === 'Very High' ? '#4caf50' : '#2196f3',
                            color: 'white',
                            fontWeight: 600,
                          }}
                        />
                      </Box>
                      <Typography variant="body2">
                        <strong>Key Skills:</strong> {career.skills.join(', ')}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Facilities */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={2600}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 2,
                color: '#1e293b',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              State-of-the-Art Facilities
            </Typography>
          </Fade>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#64748b',
              fontSize: '1.1rem',
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            Learn in modern, well-equipped laboratories with industry-standard tools and software
          </Typography>

          <Grid container spacing={4}>
            {facilities.map((facility, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Grow in timeout={2800 + index * 200}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          mb: 2,
                          color: '#1e293b',
                        }}
                      >
                        {facility.name}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          mb: 3,
                          lineHeight: 1.6,
                        }}
                      >
                        {facility.description}
                      </Typography>

                      <List dense>
                        {facility.features.map((feature, featureIndex) => (
                          <ListItem key={featureIndex} sx={{ px: 0, py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 30 }}>
                              <CheckCircle sx={{ fontSize: '1rem', color: '#1976d2' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={feature}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.9rem',
                                  color: '#64748b',
                                },
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Achievements */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Fade in timeout={3000}>
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              fontWeight: 700,
              mb: 2,
              color: '#1e293b',
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Our Achievements
          </Typography>
        </Fade>

        <Typography
          variant="body1"
          sx={{
            textAlign: 'center',
            color: '#64748b',
            fontSize: '1.1rem',
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          Recognition of excellence in computer science education and student success
        </Typography>

        <Grid container spacing={4}>
          {achievements.map((achievement, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Grow in timeout={3200 + index * 200}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${achievement.color}10 0%, ${achievement.color}05 100%)`,
                    borderRadius: 3,
                    border: `2px solid ${achievement.color}30`,
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: `0 20px 60px ${achievement.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box
                      sx={{
                        width: 70,
                        height: 70,
                        bgcolor: achievement.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 3,
                        boxShadow: `0 8px 25px ${achievement.color}40`,
                      }}
                    >
                      {achievement.icon}
                    </Box>

                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        color: '#1e293b',
                      }}
                    >
                      {achievement.title}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: '#64748b',
                        lineHeight: 1.6,
                        mb: 2,
                      }}
                    >
                      {achievement.description}
                    </Typography>

                    <Chip
                      label={achievement.year}
                      sx={{
                        bgcolor: achievement.color,
                        color: 'white',
                        fontWeight: 600,
                      }}
                    />
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={3400}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Ready to Code Your Future?
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Join our Computer Science program and become part of Rwanda's digital transformation.
            Start building the applications and systems that will shape tomorrow.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                bgcolor: 'white',
                color: '#667eea',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Apply Now
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              Download Curriculum
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default ComputerSciencePage
