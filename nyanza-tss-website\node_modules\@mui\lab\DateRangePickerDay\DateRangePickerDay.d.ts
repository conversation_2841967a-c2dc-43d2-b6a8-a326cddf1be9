import * as React from 'react';
type DateRangePickerDayComponent = (<TDate>(props: DateRangePickerDayProps & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * @deprecated The DateRangePickerDay component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.
 * @ignore - do not document.
 */
declare const DateRangePickerDay: DateRangePickerDayComponent;
export default DateRangePickerDay;
export declare const getDateRangePickerDayUtilityClass: (slot: string) => string;
export type DateRangePickerDayProps = Record<any, any>;
export type DateRangePickerDayClasses = any;
export type DateRangePickerDayClassKey = any;