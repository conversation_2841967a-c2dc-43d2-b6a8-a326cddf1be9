import React, { useState } from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Fade,
  Grow,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
  Paper,
  IconButton,
  Tabs,
  Tab,
} from '@mui/material'
import {
  Home,
  MenuBook,
  ExpandMore,
  School,
  Gavel,
  Security,
  LocalHospital,
  Restaurant,
  DirectionsBus,
  Wifi,
  Computer,
  SportsVolleyball,
  Group,
  Event,
  Download,
  Print,
  Share,
  CheckCircle,
  Info,
  Warning,
  Assignment,
  Schedule,
  ContactSupport,
  Psychology,
  Lightbulb,
} from '@mui/icons-material'

const StudentsHandbookPage = () => {
  const [selectedSection, setSelectedSection] = useState(0)

  const sections = [
    'Academic Policies',
    'Code of Conduct',
    'Student Services',
    'Campus Life',
    'Resources & Support'
  ]

  const academicPolicies = [
    {
      title: 'Attendance Policy',
      icon: <Schedule />,
      color: '#1976d2',
      content: [
        'Students must maintain 85% attendance in all courses',
        'Absences must be reported within 24 hours',
        'Medical certificates required for extended absences',
        'Excessive absences may result in academic probation'
      ]
    },
    {
      title: 'Assessment & Grading',
      icon: <Assignment />,
      color: '#4caf50',
      content: [
        'Continuous assessment: 60% of final grade',
        'Final examinations: 40% of final grade',
        'Minimum passing grade: 50%',
        'Grade appeals must be submitted within 7 days'
      ]
    },
    {
      title: 'Academic Integrity',
      icon: <Gavel />,
      color: '#ff9800',
      content: [
        'Plagiarism and cheating are strictly prohibited',
        'All work must be original and properly cited',
        'Violations result in disciplinary action',
        'Academic honesty workshops are mandatory'
      ]
    },
    {
      title: 'Progression Requirements',
      icon: <TrendingUp />,
      color: '#9c27b0',
      content: [
        'Minimum GPA of 2.5 required for progression',
        'All core modules must be completed',
        'Industrial attachment is mandatory',
        'Portfolio submission required for graduation'
      ]
    }
  ]

  const codeOfConduct = [
    {
      title: 'General Behavior',
      icon: <Psychology />,
      color: '#2196f3',
      rules: [
        'Respect for all members of the school community',
        'Professional dress code must be observed',
        'Punctuality is expected at all times',
        'Mobile phones must be on silent during classes'
      ]
    },
    {
      title: 'Academic Conduct',
      icon: <School />,
      color: '#4caf50',
      rules: [
        'Active participation in all learning activities',
        'Completion of assignments by due dates',
        'Respectful interaction with instructors and peers',
        'Proper use of academic resources and facilities'
      ]
    },
    {
      title: 'Disciplinary Measures',
      icon: <Warning />,
      color: '#f44336',
      rules: [
        'Verbal warning for minor infractions',
        'Written warning for repeated violations',
        'Suspension for serious misconduct',
        'Expulsion for severe violations'
      ]
    },
    {
      title: 'Rights & Responsibilities',
      icon: <Gavel />,
      color: '#ff9800',
      rules: [
        'Right to quality education and fair treatment',
        'Responsibility to maintain academic standards',
        'Right to appeal disciplinary decisions',
        'Responsibility to report misconduct'
      ]
    }
  ]

  const studentServices = [
    {
      name: 'Health Services',
      description: 'Comprehensive healthcare support for all students',
      icon: <LocalHospital />,
      color: '#f44336',
      services: ['First aid and emergency care', 'Health counseling', 'Medical referrals', 'Health education programs'],
      hours: 'Monday-Friday: 8:00 AM - 5:00 PM',
      contact: '<EMAIL>'
    },
    {
      name: 'Counseling Services',
      description: 'Professional guidance and psychological support',
      icon: <Psychology />,
      color: '#9c27b0',
      services: ['Personal counseling', 'Academic guidance', 'Career counseling', 'Crisis intervention'],
      hours: 'Monday-Friday: 9:00 AM - 4:00 PM',
      contact: '<EMAIL>'
    },
    {
      name: 'IT Support',
      description: 'Technical assistance and computer access',
      icon: <Computer />,
      color: '#2196f3',
      services: ['Computer lab access', 'Technical support', 'Internet connectivity', 'Software training'],
      hours: 'Monday-Friday: 7:00 AM - 6:00 PM',
      contact: '<EMAIL>'
    },
    {
      name: 'Library Services',
      description: 'Academic resources and study facilities',
      icon: <MenuBook />,
      color: '#4caf50',
      services: ['Book lending', 'Research assistance', 'Study spaces', 'Digital resources'],
      hours: 'Monday-Friday: 7:00 AM - 7:00 PM',
      contact: '<EMAIL>'
    },
    {
      name: 'Transport Services',
      description: 'Safe and reliable transportation options',
      icon: <DirectionsBus />,
      color: '#ff9800',
      services: ['Daily bus routes', 'Field trip transport', 'Emergency transport', 'Route scheduling'],
      hours: 'Monday-Friday: 6:00 AM - 7:00 PM',
      contact: '<EMAIL>'
    },
    {
      name: 'Cafeteria Services',
      description: 'Nutritious meals and dining facilities',
      icon: <Restaurant />,
      color: '#795548',
      services: ['Breakfast service', 'Lunch meals', 'Snacks and beverages', 'Special dietary needs'],
      hours: 'Monday-Friday: 6:30 AM - 6:00 PM',
      contact: '<EMAIL>'
    }
  ]

  const campusLife = [
    {
      title: 'Student Organizations',
      description: 'Join clubs and societies to enhance your experience',
      icon: <Group />,
      color: '#1976d2',
      items: ['15+ active clubs and societies', 'Leadership opportunities', 'Skill development programs', 'Community service projects']
    },
    {
      title: 'Sports & Recreation',
      description: 'Stay active with our comprehensive sports programs',
      icon: <SportsVolleyball />,
      color: '#4caf50',
      items: ['Championship volleyball team', 'Basketball and football teams', 'Athletics programs', 'Fitness facilities']
    },
    {
      title: 'Events & Activities',
      description: 'Participate in exciting campus events throughout the year',
      icon: <Event />,
      color: '#ff9800',
      items: ['Cultural festivals', 'Technical exhibitions', 'Sports competitions', 'Graduation ceremonies']
    },
    {
      title: 'Accommodation',
      description: 'Comfortable and secure housing options',
      icon: <Security />,
      color: '#9c27b0',
      items: ['On-campus dormitories', '24/7 security', 'Study areas', 'Recreation facilities']
    }
  ]

  const resources = [
    {
      title: 'Academic Resources',
      icon: <School />,
      color: '#1976d2',
      items: [
        { name: 'Course Catalog 2024', type: 'PDF', size: '2.5 MB' },
        { name: 'Academic Calendar', type: 'PDF', size: '1.2 MB' },
        { name: 'Assessment Guidelines', type: 'PDF', size: '800 KB' },
        { name: 'Research Guidelines', type: 'PDF', size: '1.5 MB' }
      ]
    },
    {
      title: 'Forms & Applications',
      icon: <Assignment />,
      color: '#4caf50',
      items: [
        { name: 'Leave Application Form', type: 'PDF', size: '500 KB' },
        { name: 'Grade Appeal Form', type: 'PDF', size: '400 KB' },
        { name: 'Club Registration Form', type: 'PDF', size: '600 KB' },
        { name: 'Accommodation Application', type: 'PDF', size: '750 KB' }
      ]
    },
    {
      title: 'Support Contacts',
      icon: <ContactSupport />,
      color: '#ff9800',
      items: [
        { name: 'Academic Office', contact: '<EMAIL>', phone: '+250 788 309 436' },
        { name: 'Student Affairs', contact: '<EMAIL>', phone: '+250 788 309 437' },
        { name: 'IT Helpdesk', contact: '<EMAIL>', phone: '+250 788 309 438' },
        { name: 'Emergency Line', contact: '<EMAIL>', phone: '+250 788 309 439' }
      ]
    }
  ]

  const quickLinks = [
    { name: 'Student Portal', icon: <Computer />, color: '#1976d2' },
    { name: 'Course Materials', icon: <MenuBook />, color: '#4caf50' },
    { name: 'Academic Calendar', icon: <Schedule />, color: '#ff9800' },
    { name: 'Library Catalog', icon: <MenuBook />, color: '#9c27b0' },
    { name: 'Health Services', icon: <LocalHospital />, color: '#f44336' },
    { name: 'IT Support', icon: <Computer />, color: '#2196f3' }
  ]

  return (
    <Box sx={{ minHeight: '100vh', pt: 10 }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5)), url("https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Animated Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 20% 80%, rgba(25, 118, 210, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(76, 175, 80, 0.3) 0%, transparent 50%)',
            animation: 'float 20s ease-in-out infinite',
            '@keyframes float': {
              '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
              '50%': { transform: 'translateY(-20px) rotate(1deg)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{ 
              mb: 3,
              '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' },
              '& .MuiLink-root': { color: 'rgba(255,255,255,0.8)' },
            }}
          >
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              onClick={() => window.location.href = '/'}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Home
            </Link>
            <Typography color="white" sx={{ display: 'flex', alignItems: 'center' }}>
              <MenuBook sx={{ mr: 0.5 }} fontSize="inherit" />
              Students Handbook
            </Typography>
          </Breadcrumbs>

          <Fade in timeout={1000}>
            <Typography
              variant="h1"
              sx={{
                fontWeight: 800,
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                lineHeight: 1.2,
                animation: 'glow 3s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)' },
                  '100%': { textShadow: '0 4px 30px rgba(255, 255, 255, 0.5)' },
                },
              }}
            >
              Students Handbook
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: '800px',
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              lineHeight: 1.6,
              animation: 'slideInUp 1s ease-out 0.5s both',
              '@keyframes slideInUp': {
                '0%': { opacity: 0, transform: 'translateY(30px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            Your comprehensive guide to academic policies, student services, campus life, and everything 
            you need to know for a successful experience at Nyanza TSS.
          </Typography>

          {/* Quick Action Buttons */}
          <Box sx={{ display: 'flex', gap: 3, mt: 4, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<Download />}
              sx={{
                bgcolor: 'white',
                color: '#1976d2',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Download PDF
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<Print />}
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              Print Version
            </Button>
          </Box>
        </Container>
      </Box>

      {/* Quick Links */}
      <Container maxWidth="lg" sx={{ py: { xs: 4, md: 6 } }}>
        <Typography
          variant="h4"
          sx={{
            textAlign: 'center',
            fontWeight: 700,
            mb: 4,
            color: '#1e293b',
          }}
        >
          Quick Access
        </Typography>

        <Grid container spacing={3}>
          {quickLinks.map((link, index) => (
            <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
              <Grow in timeout={1200 + index * 100}>
                <Card
                  sx={{
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${link.color}10 0%, ${link.color}05 100%)`,
                    borderRadius: 3,
                    border: `2px solid ${link.color}30`,
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    cursor: 'pointer',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: `0 20px 60px ${link.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        bgcolor: link.color,
                        borderRadius: 3,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                        boxShadow: `0 8px 25px ${link.color}40`,
                        animation: 'pulse 2s ease-in-out infinite',
                        '@keyframes pulse': {
                          '0%, 100%': { transform: 'scale(1)' },
                          '50%': { transform: 'scale(1.05)' },
                        },
                      }}
                    >
                      {link.icon}
                    </Box>
                    <Typography variant="body2" color="#64748b" fontWeight={600}>
                      {link.name}
                    </Typography>
                  </CardContent>
                </Card>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Main Content Sections */}
      <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 6 }}>
          <Tabs
            value={selectedSection}
            onChange={(e, newValue) => setSelectedSection(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem',
                minWidth: 140,
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                },
              },
              '& .Mui-selected': {
                color: '#1976d2',
              },
              '& .MuiTabs-indicator': {
                backgroundColor: '#1976d2',
                height: 3,
                borderRadius: 2,
              },
            }}
          >
            {sections.map((section, index) => (
              <Tab key={index} label={section} />
            ))}
          </Tabs>
        </Box>

        {/* Academic Policies Section */}
        {selectedSection === 0 && (
          <Container maxWidth="lg">
            <Typography
              variant="h4"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 4,
                color: '#1e293b',
              }}
            >
              Academic Policies
            </Typography>

            <Grid container spacing={4}>
              {academicPolicies.map((policy, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <Grow in timeout={1600 + index * 200}>
                    <Card
                      sx={{
                        height: '100%',
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: 3,
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                        },
                      }}
                    >
                      <CardContent sx={{ p: 4 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Box
                            sx={{
                              width: 60,
                              height: 60,
                              bgcolor: policy.color,
                              borderRadius: 3,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              mr: 3,
                              boxShadow: `0 8px 25px ${policy.color}40`,
                            }}
                          >
                            {policy.icon}
                          </Box>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 600,
                              color: '#1e293b',
                            }}
                          >
                            {policy.title}
                          </Typography>
                        </Box>

                        <List>
                          {policy.content.map((item, itemIndex) => (
                            <ListItem key={itemIndex} sx={{ px: 0, py: 0.5 }}>
                              <ListItemIcon sx={{ minWidth: 30 }}>
                                <CheckCircle sx={{ fontSize: '1rem', color: policy.color }} />
                              </ListItemIcon>
                              <ListItemText
                                primary={item}
                                sx={{
                                  '& .MuiListItemText-primary': {
                                    fontSize: '0.9rem',
                                    color: '#64748b',
                                    lineHeight: 1.5,
                                  },
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>
              ))}
            </Grid>
          </Container>
        )}

        {/* Code of Conduct Section */}
        {selectedSection === 1 && (
          <Container maxWidth="lg">
            <Typography
              variant="h4"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 4,
                color: '#1e293b',
              }}
            >
              Code of Conduct
            </Typography>

            <Grid container spacing={4}>
              {codeOfConduct.map((conduct, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <Grow in timeout={1600 + index * 200}>
                    <Card
                      sx={{
                        height: '100%',
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: 3,
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                        },
                      }}
                    >
                      <CardContent sx={{ p: 4 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Box
                            sx={{
                              width: 60,
                              height: 60,
                              bgcolor: conduct.color,
                              borderRadius: 3,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              mr: 3,
                              boxShadow: `0 8px 25px ${conduct.color}40`,
                            }}
                          >
                            {conduct.icon}
                          </Box>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 600,
                              color: '#1e293b',
                            }}
                          >
                            {conduct.title}
                          </Typography>
                        </Box>

                        <List>
                          {conduct.rules.map((rule, ruleIndex) => (
                            <ListItem key={ruleIndex} sx={{ px: 0, py: 0.5 }}>
                              <ListItemIcon sx={{ minWidth: 30 }}>
                                <CheckCircle sx={{ fontSize: '1rem', color: conduct.color }} />
                              </ListItemIcon>
                              <ListItemText
                                primary={rule}
                                sx={{
                                  '& .MuiListItemText-primary': {
                                    fontSize: '0.9rem',
                                    color: '#64748b',
                                    lineHeight: 1.5,
                                  },
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>
              ))}
            </Grid>
          </Container>
        )}

        {/* Student Services Section */}
        {selectedSection === 2 && (
          <Container maxWidth="lg">
            <Typography
              variant="h4"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 4,
                color: '#1e293b',
              }}
            >
              Student Services
            </Typography>

            <Grid container spacing={4}>
              {studentServices.map((service, index) => (
                <Grid item xs={12} md={6} lg={4} key={index}>
                  <Grow in timeout={1600 + index * 200}>
                    <Card
                      sx={{
                        height: '100%',
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: 3,
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                        },
                      }}
                    >
                      <CardContent sx={{ p: 4 }}>
                        <Box sx={{ textAlign: 'center', mb: 3 }}>
                          <Box
                            sx={{
                              width: 80,
                              height: 80,
                              bgcolor: service.color,
                              borderRadius: '50%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              mx: 'auto',
                              mb: 2,
                              boxShadow: `0 8px 25px ${service.color}40`,
                              fontSize: '2rem',
                            }}
                          >
                            {service.icon}
                          </Box>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 600,
                              color: '#1e293b',
                              mb: 1,
                            }}
                          >
                            {service.name}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              color: '#64748b',
                              mb: 2,
                            }}
                          >
                            {service.description}
                          </Typography>
                        </Box>

                        <List dense>
                          {service.services.map((item, itemIndex) => (
                            <ListItem key={itemIndex} sx={{ px: 0, py: 0.25 }}>
                              <ListItemIcon sx={{ minWidth: 25 }}>
                                <CheckCircle sx={{ fontSize: '0.8rem', color: service.color }} />
                              </ListItemIcon>
                              <ListItemText
                                primary={item}
                                sx={{
                                  '& .MuiListItemText-primary': {
                                    fontSize: '0.8rem',
                                    color: '#64748b',
                                  },
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>

                        <Divider sx={{ my: 2 }} />

                        <Typography variant="body2" color="#64748b" mb={1}>
                          <strong>Hours:</strong> {service.hours}
                        </Typography>
                        <Typography variant="body2" color={service.color} fontWeight={500}>
                          <strong>Contact:</strong> {service.contact}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>
              ))}
            </Grid>
          </Container>
        )}

        {/* Campus Life Section */}
        {selectedSection === 3 && (
          <Container maxWidth="lg">
            <Typography
              variant="h4"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 4,
                color: '#1e293b',
              }}
            >
              Campus Life
            </Typography>

            <Grid container spacing={4}>
              {campusLife.map((life, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <Grow in timeout={1600 + index * 200}>
                    <Card
                      sx={{
                        height: '100%',
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: 3,
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                        },
                      }}
                    >
                      <CardContent sx={{ p: 4 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Box
                            sx={{
                              width: 60,
                              height: 60,
                              bgcolor: life.color,
                              borderRadius: 3,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              mr: 3,
                              boxShadow: `0 8px 25px ${life.color}40`,
                            }}
                          >
                            {life.icon}
                          </Box>
                          <Box>
                            <Typography
                              variant="h6"
                              sx={{
                                fontWeight: 600,
                                color: '#1e293b',
                                mb: 1,
                              }}
                            >
                              {life.title}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                color: '#64748b',
                              }}
                            >
                              {life.description}
                            </Typography>
                          </Box>
                        </Box>

                        <List>
                          {life.items.map((item, itemIndex) => (
                            <ListItem key={itemIndex} sx={{ px: 0, py: 0.5 }}>
                              <ListItemIcon sx={{ minWidth: 30 }}>
                                <CheckCircle sx={{ fontSize: '1rem', color: life.color }} />
                              </ListItemIcon>
                              <ListItemText
                                primary={item}
                                sx={{
                                  '& .MuiListItemText-primary': {
                                    fontSize: '0.9rem',
                                    color: '#64748b',
                                    lineHeight: 1.5,
                                  },
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>
              ))}
            </Grid>
          </Container>
        )}

        {/* Resources & Support Section */}
        {selectedSection === 4 && (
          <Container maxWidth="lg">
            <Typography
              variant="h4"
              sx={{
                textAlign: 'center',
                fontWeight: 700,
                mb: 4,
                color: '#1e293b',
              }}
            >
              Resources & Support
            </Typography>

            <Grid container spacing={4}>
              {resources.map((resource, index) => (
                <Grid item xs={12} md={4} key={index}>
                  <Grow in timeout={1600 + index * 200}>
                    <Card
                      sx={{
                        height: '100%',
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: 3,
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
                        },
                      }}
                    >
                      <CardContent sx={{ p: 4 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Box
                            sx={{
                              width: 50,
                              height: 50,
                              bgcolor: resource.color,
                              borderRadius: 2,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              mr: 2,
                              boxShadow: `0 8px 25px ${resource.color}40`,
                            }}
                          >
                            {resource.icon}
                          </Box>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 600,
                              color: '#1e293b',
                            }}
                          >
                            {resource.title}
                          </Typography>
                        </Box>

                        <List dense>
                          {resource.items.map((item, itemIndex) => (
                            <ListItem key={itemIndex} sx={{ px: 0, py: 1, flexDirection: 'column', alignItems: 'flex-start' }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mb: 0.5 }}>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontWeight: 500,
                                    color: '#1e293b',
                                    flex: 1,
                                  }}
                                >
                                  {item.name}
                                </Typography>
                                {item.type && (
                                  <Chip
                                    label={item.type}
                                    size="small"
                                    sx={{
                                      bgcolor: `${resource.color}20`,
                                      color: resource.color,
                                      fontSize: '0.7rem',
                                    }}
                                  />
                                )}
                              </Box>
                              {item.size && (
                                <Typography variant="caption" color="#64748b">
                                  Size: {item.size}
                                </Typography>
                              )}
                              {item.contact && (
                                <Typography variant="caption" color={resource.color}>
                                  {item.contact}
                                </Typography>
                              )}
                              {item.phone && (
                                <Typography variant="caption" color="#64748b">
                                  Phone: {item.phone}
                                </Typography>
                              )}
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>
              ))}
            </Grid>
          </Container>
        )}
      </Container>

      {/* Call to Action */}
      <Box
        sx={{
          py: { xs: 6, md: 8 },
          background: 'linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6)), url("https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=1920&h=1080&fit=crop")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Fade in timeout={2800}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                mb: 3,
                color: 'white',
                fontSize: { xs: '2rem', md: '2.5rem' },
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
              }}
            >
              Need Additional Support?
            </Typography>
          </Fade>

          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Our student support team is here to help you succeed. Contact us for any questions
            or assistance you may need during your studies.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<ContactSupport />}
              sx={{
                bgcolor: 'white',
                color: '#1976d2',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 35px rgba(255, 255, 255, 0.4)',
                },
              }}
            >
              Contact Support
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<MenuBook />}
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-3px)',
                },
              }}
            >
              Student Portal
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default StudentsHandbookPage
