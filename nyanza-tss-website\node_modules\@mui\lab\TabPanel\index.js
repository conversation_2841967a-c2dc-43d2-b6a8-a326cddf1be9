"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  tabPanelClasses: true
};
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _TabPanel.default;
  }
});
Object.defineProperty(exports, "tabPanelClasses", {
  enumerable: true,
  get: function () {
    return _tabPanelClasses.default;
  }
});
var _TabPanel = _interopRequireDefault(require("./TabPanel"));
var _tabPanelClasses = _interopRequireWildcard(require("./tabPanelClasses"));
Object.keys(_tabPanelClasses).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _tabPanelClasses[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _tabPanelClasses[key];
    }
  });
});